<?php

namespace app\modules\employer\controllers;

use Yii;
use yii\rest\Controller;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\ContentNegotiator;
use yii\web\Response;
use app\modules\employer\services\PaymentService;
use app\modules\employer\models\Payment;
use app\modules\common\models\ApiResponse;

/**
 * Контроллер для обработки платежей
 */
class PaymentController extends Controller
{
    /**
     * @var PaymentService
     */
    private $paymentService;

    public function init()
    {
        parent::init();
        $this->paymentService = new PaymentService();
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        
        // Убираем аутентификацию для callback endpoints
        $behaviors['authenticator'] = [
            'class' => HttpBearerAuth::class,
            'except' => ['callback', 'click-callback', 'payme-callback', 'uzcard-callback']
        ];
        
        $behaviors['contentNegotiator'] = [
            'class' => ContentNegotiator::class,
            'formats' => [
                'application/json' => Response::FORMAT_JSON,
            ],
        ];

        return $behaviors;
    }

    /**
     * Получить поддерживаемые методы оплаты
     * GET /employer/payment/methods
     */
    public function actionMethods()
    {
        try {
            $methods = $this->paymentService->getSupportedMethods();

            return ApiResponse::success([
                'payment_methods' => $methods,
                'default_method' => 'click'
            ]);

        } catch (\Exception $e) {
            Yii::error('Error getting payment methods: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get payment methods', 500);
        }
    }

    /**
     * Получить статус платежа
     * GET /employer/payment/status
     */
    public function actionStatus()
    {
        try {
            $paymentId = Yii::$app->request->get('payment_id');

            if (!$paymentId) {
                return ApiResponse::error('Payment ID is required');
            }

            $status = $this->paymentService->getPaymentStatus($paymentId);

            if (!$status['found']) {
                return ApiResponse::error($status['message'], 404);
            }

            return ApiResponse::success($status);

        } catch (\Exception $e) {
            Yii::error('Error getting payment status: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get payment status', 500);
        }
    }

    /**
     * Получить историю платежей работодателя
     * GET /employer/payment/history
     */
    public function actionHistory()
    {
        try {
            $employerId = Yii::$app->user->id;
            $status = Yii::$app->request->get('status');
            $page = (int)Yii::$app->request->get('page', 1);
            $limit = (int)Yii::$app->request->get('limit', 20);

            $payments = Payment::getByEmployer($employerId, $status);

            // Пагинация
            $totalCount = count($payments);
            $offset = ($page - 1) * $limit;
            $paginatedPayments = array_slice($payments, $offset, $limit);

            $result = [];
            foreach ($paginatedPayments as $payment) {
                $result[] = $payment->toApiArray();
            }

            return ApiResponse::success([
                'payments' => $result,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total_count' => $totalCount,
                    'total_pages' => ceil($totalCount / $limit),
                ],
                'filters' => [
                    'status' => $status
                ]
            ]);

        } catch (\Exception $e) {
            Yii::error('Error getting payment history: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get payment history', 500);
        }
    }

    /**
     * Общий callback для всех платежных систем
     * POST /employer/payment/callback
     */
    public function actionCallback()
    {
        try {
            $method = Yii::$app->request->get('method', 'click');
            $callbackData = Yii::$app->request->getBodyParams();

            if (empty($callbackData)) {
                $callbackData = Yii::$app->request->get();
            }

            Yii::info('Payment callback received: ' . json_encode([
                'method' => $method,
                'data' => $callbackData
            ]), __METHOD__);

            $result = $this->paymentService->processPaymentCallback($method, $callbackData);

            return ApiResponse::success($result);

        } catch (\Exception $e) {
            Yii::error('Error processing payment callback: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Callback processing failed', 500);
        }
    }

    /**
     * Callback для Click платежей
     * POST /employer/payment/click-callback
     */
    public function actionClickCallback()
    {
        try {
            $callbackData = Yii::$app->request->getBodyParams();
            if (empty($callbackData)) {
                $callbackData = Yii::$app->request->get();
            }

            Yii::info('Click callback received: ' . json_encode($callbackData), __METHOD__);

            $result = $this->paymentService->processPaymentCallback('click', $callbackData);

            // Click ожидает специфичный формат ответа
            return [
                'error' => $result['status'] === 'completed' ? 0 : -1,
                'error_note' => $result['message'] ?? ''
            ];

        } catch (\Exception $e) {
            Yii::error('Error processing Click callback: ' . $e->getMessage(), __METHOD__);
            return [
                'error' => -1,
                'error_note' => 'Internal server error'
            ];
        }
    }

    /**
     * Callback для Payme платежей
     * POST /employer/payment/payme-callback
     */
    public function actionPaymeCallback()
    {
        try {
            $callbackData = Yii::$app->request->getBodyParams();

            Yii::info('Payme callback received: ' . json_encode($callbackData), __METHOD__);

            $result = $this->paymentService->processPaymentCallback('payme', $callbackData);

            // Payme использует JSON-RPC формат
            return [
                'jsonrpc' => '2.0',
                'id' => $callbackData['id'] ?? null,
                'result' => $result
            ];

        } catch (\Exception $e) {
            Yii::error('Error processing Payme callback: ' . $e->getMessage(), __METHOD__);
            return [
                'jsonrpc' => '2.0',
                'id' => $callbackData['id'] ?? null,
                'error' => [
                    'code' => -32603,
                    'message' => 'Internal error'
                ]
            ];
        }
    }

    /**
     * Callback для UzCard платежей
     * POST /employer/payment/uzcard-callback
     */
    public function actionUzcardCallback()
    {
        try {
            $callbackData = Yii::$app->request->getBodyParams();
            if (empty($callbackData)) {
                $callbackData = Yii::$app->request->get();
            }

            Yii::info('UzCard callback received: ' . json_encode($callbackData), __METHOD__);

            $result = $this->paymentService->processPaymentCallback('uzcard', $callbackData);

            return ApiResponse::success($result);

        } catch (\Exception $e) {
            Yii::error('Error processing UzCard callback: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Callback processing failed', 500);
        }
    }

    /**
     * Получить статистику платежей
     * GET /employer/payment/statistics
     */
    public function actionStatistics()
    {
        try {
            $filters = [
                'date_from' => Yii::$app->request->get('date_from'),
                'date_to' => Yii::$app->request->get('date_to'),
                'method' => Yii::$app->request->get('method'),
            ];

            $statistics = $this->paymentService->getPaymentStatistics($filters);

            return ApiResponse::success([
                'statistics' => $statistics,
                'filters_applied' => array_filter($filters),
                'generated_at' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            Yii::error('Error getting payment statistics: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get payment statistics', 500);
        }
    }

    /**
     * Повторить неудачный платеж
     * POST /employer/payment/retry
     */
    public function actionRetry()
    {
        try {
            $paymentId = Yii::$app->request->getBodyParam('payment_id');
            $returnUrl = Yii::$app->request->getBodyParam('return_url', '');

            if (!$paymentId) {
                return ApiResponse::error('Payment ID is required');
            }

            $payment = Payment::findOne($paymentId);
            if (!$payment) {
                return ApiResponse::error('Payment not found', 404);
            }

            if ($payment->employer_id !== Yii::$app->user->id) {
                return ApiResponse::error('Access denied', 403);
            }

            if (!$payment->isFailed()) {
                return ApiResponse::error('Only failed payments can be retried');
            }

            // Создаем новый платеж с теми же параметрами
            $retryResult = $this->paymentService->initiatePayment(
                $payment->employer_id,
                $payment->amount,
                $payment->payment_method,
                $returnUrl,
                $payment->purchase_id
            );

            return ApiResponse::success([
                'original_payment_id' => $paymentId,
                'retry_payment' => $retryResult
            ]);

        } catch (\Exception $e) {
            Yii::error('Error retrying payment: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to retry payment', 500);
        }
    }
}
