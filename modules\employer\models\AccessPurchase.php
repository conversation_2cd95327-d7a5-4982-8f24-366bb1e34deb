<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель покупки доступа к работникам
 * 
 * @property int $id
 * @property int $employer_id
 * @property int $tariff_plan_id
 * @property int $total_workers_count
 * @property float $base_price
 * @property float $discount_amount
 * @property float $final_price
 * @property string $starts_at
 * @property string $expires_at
 * @property string $status
 * @property string $created_at
 * 
 * @property Employer $employer
 * @property TariffPlan $tariffPlan
 * @property PurchaseWorker[] $purchaseWorkers
 * @property Payment[] $payments
 */
class AccessPurchase extends ActiveRecord
{
    // Статусы покупки
    const STATUS_ACTIVE = 'active';
    const STATUS_EXPIRED = 'expired';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_PENDING = 'pending';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%worker_access_purchases}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['employer_id', 'tariff_plan_id', 'total_workers_count', 'base_price', 'final_price', 'starts_at', 'expires_at'], 'required'],
            [['employer_id', 'tariff_plan_id', 'total_workers_count'], 'integer'],
            [['base_price', 'discount_amount', 'final_price'], 'number'],
            [['starts_at', 'expires_at', 'created_at'], 'safe'],
            [['status'], 'string', 'max' => 20],
            [['status'], 'in', 'range' => [self::STATUS_ACTIVE, self::STATUS_EXPIRED, self::STATUS_CANCELLED, self::STATUS_PENDING]],
            [['status'], 'default', 'value' => self::STATUS_PENDING],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'employer_id' => 'Работодатель',
            'tariff_plan_id' => 'Тарифный план',
            'total_workers_count' => 'Количество работников',
            'base_price' => 'Базовая цена',
            'discount_amount' => 'Размер скидки',
            'final_price' => 'Итоговая цена',
            'starts_at' => 'Дата начала',
            'expires_at' => 'Дата окончания',
            'status' => 'Статус',
            'created_at' => 'Дата создания',
        ];
    }

    /**
     * Связь с работодателем
     */
    public function getEmployer()
    {
        return $this->hasOne(Employer::class, ['id' => 'employer_id']);
    }

    /**
     * Связь с тарифным планом
     */
    public function getTariffPlan()
    {
        return $this->hasOne(TariffPlan::class, ['id' => 'tariff_plan_id']);
    }

    /**
     * Связь с работниками в покупке
     */
    public function getPurchaseWorkers()
    {
        return $this->hasMany(PurchaseWorker::class, ['purchase_id' => 'id']);
    }

    /**
     * Связь с платежами
     */
    public function getPayments()
    {
        return $this->hasMany(Payment::class, ['purchase_id' => 'id']);
    }

    /**
     * Проверить, активна ли покупка
     */
    public function isActive()
    {
        return $this->status === self::STATUS_ACTIVE && 
               strtotime($this->expires_at) > time();
    }

    /**
     * Проверить, истекла ли покупка
     */
    public function isExpired()
    {
        return $this->status === self::STATUS_EXPIRED || 
               strtotime($this->expires_at) <= time();
    }

    /**
     * Получить активные покупки работодателя
     */
    public static function getActiveByEmployer($employerId)
    {
        return static::find()
            ->where([
                'employer_id' => $employerId,
                'status' => self::STATUS_ACTIVE
            ])
            ->andWhere(['>', 'expires_at', date('Y-m-d H:i:s')])
            ->all();
    }

    /**
     * Создать новую покупку
     */
    public static function createPurchase($employerId, $tariffPlanId, $workerIds, $pricing)
    {
        $transaction = \Yii::$app->db->beginTransaction();
        
        try {
            $tariff = TariffPlan::findOne($tariffPlanId);
            if (!$tariff) {
                throw new \Exception('Tariff plan not found');
            }

            $purchase = new static();
            $purchase->employer_id = $employerId;
            $purchase->tariff_plan_id = $tariffPlanId;
            $purchase->total_workers_count = count($workerIds);
            $purchase->base_price = $pricing['base_price'];
            $purchase->discount_amount = $pricing['discount_amount'];
            $purchase->final_price = $pricing['final_price'];
            $purchase->starts_at = date('Y-m-d H:i:s');
            $purchase->expires_at = date('Y-m-d H:i:s', strtotime('+' . $tariff->duration_days . ' days'));
            $purchase->status = self::STATUS_PENDING;

            if (!$purchase->save()) {
                throw new \Exception('Failed to save purchase: ' . json_encode($purchase->errors));
            }

            // Добавляем работников в покупку
            foreach ($workerIds as $workerId) {
                $purchaseWorker = new PurchaseWorker();
                $purchaseWorker->purchase_id = $purchase->id;
                $purchaseWorker->worker_id = $workerId;
                $purchaseWorker->individual_price = $pricing['price_per_worker'];
                
                if (!$purchaseWorker->save()) {
                    throw new \Exception('Failed to save purchase worker: ' . json_encode($purchaseWorker->errors));
                }
            }

            $transaction->commit();
            return $purchase;

        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * Активировать покупку после успешной оплаты
     */
    public function activate()
    {
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * Отменить покупку
     */
    public function cancel()
    {
        $this->status = self::STATUS_CANCELLED;
        return $this->save();
    }

    /**
     * Форматировать для API
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'employer_id' => $this->employer_id,
            'tariff_plan' => $this->tariffPlan ? [
                'id' => $this->tariffPlan->id,
                'name' => $this->tariffPlan->name,
                'duration_days' => $this->tariffPlan->duration_days,
            ] : null,
            'total_workers_count' => $this->total_workers_count,
            'base_price' => (float)$this->base_price,
            'discount_amount' => (float)$this->discount_amount,
            'final_price' => (float)$this->final_price,
            'starts_at' => $this->starts_at,
            'expires_at' => $this->expires_at,
            'status' => $this->status,
            'is_active' => $this->isActive(),
            'is_expired' => $this->isExpired(),
            'created_at' => $this->created_at,
        ];
    }
}
