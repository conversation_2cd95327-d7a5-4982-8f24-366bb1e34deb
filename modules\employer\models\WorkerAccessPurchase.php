<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель покупки доступа к работникам
 * 
 * @property int $id
 * @property int $employer_id
 * @property int $tariff_plan_id
 * @property int $total_workers_count
 * @property float $base_price
 * @property float $discount_amount
 * @property float $final_price
 * @property string $starts_at
 * @property string $expires_at
 * @property string $status
 * @property string $created_at
 * 
 * @property Employer $employer
 * @property TariffPlan $tariffPlan
 * @property PurchaseWorker[] $purchaseWorkers
 * @property Payment[] $payments
 */
class WorkerAccessPurchase extends ActiveRecord
{
    // Константы статусов
    const STATUS_ACTIVE = 'active';
    const STATUS_EXPIRED = 'expired';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%worker_access_purchases}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['employer_id', 'tariff_plan_id', 'total_workers_count', 'base_price', 'final_price', 'starts_at', 'expires_at'], 'required'],
            [['employer_id', 'tariff_plan_id', 'total_workers_count'], 'integer'],
            [['base_price', 'discount_amount', 'final_price'], 'number'],
            [['starts_at', 'expires_at', 'created_at'], 'safe'],
            [['status'], 'string', 'max' => 20],
            [['status'], 'in', 'range' => [self::STATUS_ACTIVE, self::STATUS_EXPIRED, self::STATUS_CANCELLED]],
            [['status'], 'default', 'value' => self::STATUS_ACTIVE],
            [['discount_amount'], 'default', 'value' => 0],
            [['total_workers_count'], 'integer', 'min' => 1],
            [['base_price', 'final_price'], 'number', 'min' => 0],
            [['discount_amount'], 'number', 'min' => 0],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'employer_id' => 'Работодатель',
            'tariff_plan_id' => 'Тарифный план',
            'total_workers_count' => 'Количество работников',
            'base_price' => 'Базовая цена',
            'discount_amount' => 'Размер скидки',
            'final_price' => 'Итоговая цена',
            'starts_at' => 'Дата начала',
            'expires_at' => 'Дата окончания',
            'status' => 'Статус',
            'created_at' => 'Дата создания',
        ];
    }

    /**
     * Получение работодателя
     */
    public function getEmployer()
    {
        return $this->hasOne(Employer::class, ['id' => 'employer_id']);
    }

    /**
     * Получение тарифного плана
     */
    public function getTariffPlan()
    {
        return $this->hasOne(TariffPlan::class, ['id' => 'tariff_plan_id']);
    }

    /**
     * Получение работников в покупке
     */
    public function getPurchaseWorkers()
    {
        return $this->hasMany(PurchaseWorker::class, ['purchase_id' => 'id']);
    }

    /**
     * Получение платежей
     */
    public function getPayments()
    {
        return $this->hasMany(Payment::class, ['purchase_id' => 'id']);
    }

    /**
     * Проверить, активна ли покупка
     * 
     * @return bool
     */
    public function isActive()
    {
        return $this->status === self::STATUS_ACTIVE && 
               strtotime($this->expires_at) > time();
    }

    /**
     * Проверить, истекла ли покупка
     * 
     * @return bool
     */
    public function isExpired()
    {
        return $this->status === self::STATUS_EXPIRED || 
               strtotime($this->expires_at) <= time();
    }

    /**
     * Получить количество использованных доступов
     * 
     * @return int
     */
    public function getAccessedWorkersCount()
    {
        return PurchaseWorker::find()
            ->where(['purchase_id' => $this->id])
            ->andWhere(['not', ['accessed_at' => null]])
            ->count();
    }

    /**
     * Получить количество оставшихся доступов
     * 
     * @return int
     */
    public function getRemainingAccessCount()
    {
        return max(0, $this->total_workers_count - $this->getAccessedWorkersCount());
    }

    /**
     * Проверить доступ к конкретному работнику
     * 
     * @param int $workerId
     * @return bool
     */
    public function hasAccessToWorker($workerId)
    {
        if (!$this->isActive()) {
            return false;
        }

        return PurchaseWorker::find()
            ->where([
                'purchase_id' => $this->id,
                'worker_id' => $workerId
            ])
            ->exists();
    }

    /**
     * Отметить доступ к работнику как использованный
     * 
     * @param int $workerId
     * @return bool
     */
    public function markWorkerAccessed($workerId)
    {
        $purchaseWorker = PurchaseWorker::find()
            ->where([
                'purchase_id' => $this->id,
                'worker_id' => $workerId,
                'accessed_at' => null
            ])
            ->one();

        if ($purchaseWorker) {
            $purchaseWorker->accessed_at = date('Y-m-d H:i:s');
            return $purchaseWorker->save();
        }

        return false;
    }

    /**
     * Получить активные покупки работодателя
     * 
     * @param int $employerId
     * @return array
     */
    public static function getActiveByEmployer($employerId)
    {
        return static::find()
            ->where([
                'employer_id' => $employerId,
                'status' => self::STATUS_ACTIVE
            ])
            ->andWhere(['>', 'expires_at', date('Y-m-d H:i:s')])
            ->orderBy('created_at DESC')
            ->all();
    }

    /**
     * Форматировать для API
     * 
     * @return array
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'tariff_plan' => $this->tariffPlan ? $this->tariffPlan->toApiArray() : null,
            'total_workers_count' => $this->total_workers_count,
            'accessed_workers_count' => $this->getAccessedWorkersCount(),
            'remaining_access_count' => $this->getRemainingAccessCount(),
            'base_price' => $this->base_price,
            'discount_amount' => $this->discount_amount,
            'final_price' => $this->final_price,
            'starts_at' => $this->starts_at,
            'expires_at' => $this->expires_at,
            'status' => $this->status,
            'is_active' => $this->isActive(),
            'created_at' => $this->created_at,
        ];
    }
}
