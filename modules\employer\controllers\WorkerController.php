<?php

namespace app\modules\employer\controllers;

use app\modules\employer\services\WorkerViewService;
use app\modules\employer\services\LoggingService;
use app\modules\employer\services\AccessControlService;
use yii\web\Response;

/**
 * Контроллер для просмотра работников
 */
class WorkerController extends BaseApiController
{
    /**
     * @var WorkerViewService
     */
    private $workerViewService;

    /**
     * @var LoggingService
     */
    private $loggingService;

    /**
     * @var AccessControlService
     */
    private $accessService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->workerViewService = new WorkerViewService();
        $this->loggingService = new LoggingService();
        $this->accessService = new AccessControlService();
    }

    /**
     * Список работников с фильтрацией (публичный доступ)
     *
     * GET /employer/worker/index
     *
     * @return Response
     */
    public function actionIndex()
    {
        $filters = $this->getRequestData();
        $page = (int)($filters['page'] ?? 1);
        $limit = min((int)($filters['limit'] ?? 20), 50); // Максимум 50 записей за раз

        // Добавляем ID работодателя в фильтры для проверки доступа к телефонам
        $employer = $this->getCurrentEmployer();
        if ($employer) {
            $filters['employer_id'] = $employer->id;
        }

        try {
            $result = $this->workerViewService->getWorkersList($filters, $page, $limit);

            // Логируем поиск только если пользователь авторизован
            if ($employer) {
                $this->loggingService->logWorkerSearch(
                    $employer,
                    $filters,
                    $result['pagination']['total_count']
                );
            }

            return $this->sendSuccess($result, $this->t('app', 'Workers list retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving workers list: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve workers list'),
                null,
                500
            );
        }
    }

    /**
     * Детали работника (требует авторизации)
     *
     * GET /employer/worker/detail?id=1
     *
     * @return Response
     */
    public function actionView()
    {
        $employer = $this->requireAuth();
        
        $id = $this->getRequestData('id');
        
        if (empty($id)) {
            return $this->sendValidationError([
                'id' => [$this->t('app', 'Worker ID is required')]
            ]);
        }

        try {
            $workerDetails = $this->workerViewService->getWorkerDetails($id, $employer->id);

            if (!$workerDetails) {
                return $this->sendNotFound($this->t('app', 'Worker not found'));
            }

            // Логируем просмотр
            $this->loggingService->logWorkerView($employer, $id);

            return $this->sendSuccess($workerDetails, $this->t('app', 'Worker details retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving worker details: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve worker details'),
                null,
                500
            );
        }
    }

    /**
     * Получить список профессий для фильтрации (публичный доступ)
     *
     * GET /employer/worker/professions
     *
     * @return Response
     */
    public function actionProfessions()
    {
        try {
            $professions = $this->workerViewService->getProfessionsForFilter();

            return $this->sendSuccess([
                'professions' => $professions
            ], $this->t('app', 'Professions list retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving professions: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve professions'),
                null,
                500
            );
        }
    }

    /**
     * Поиск работников по профессии (публичный доступ)
     *
     * GET /employer/worker/by-profession
     *
     * @return Response
     */
    public function actionByProfession()
    {
        $professionIds = $this->getRequestData('profession_ids');
        $limit = min((int)($this->getRequestData('limit') ?? 10), 20);

        if (empty($professionIds)) {
            return $this->sendValidationError([
                'profession_ids' => [$this->t('app', 'Profession IDs are required')]
            ]);
        }

        if (!is_array($professionIds)) {
            $professionIds = [$professionIds];
        }

        try {
            $workers = $this->workerViewService->getWorkersByProfession($professionIds, $limit);

            return $this->sendSuccess([
                'workers' => $workers,
                'profession_ids' => $professionIds,
                'count' => count($workers)
            ], $this->t('app', 'Workers by profession retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving workers by profession: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve workers by profession'),
                null,
                500
            );
        }
    }

    /**
     * Статистика работников (публичный доступ)
     *
     * GET /employer/worker/statistics
     *
     * @return Response
     */
    public function actionStatistics()
    {
        try {
            $statistics = $this->workerViewService->getWorkersStatistics();

            return $this->sendSuccess($statistics, $this->t('app', 'Workers statistics retrieved successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error retrieving workers statistics: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to retrieve workers statistics'),
                null,
                500
            );
        }
    }

    /**
     * Проверка доступности детального просмотра для неавторизованного пользователя
     * Возвращает ошибку с предложением зарегистрироваться
     *
     * GET /employer/worker/check-access?id=1
     *
     * @return Response
     */
    public function actionCheckAccess()
    {
        $id = $this->getRequestData('id');
        
        if (empty($id)) {
            return $this->sendValidationError([
                'id' => [$this->t('app', 'Worker ID is required')]
            ]);
        }
        
        $employer = $this->getCurrentEmployer();

        if ($employer) {
            // Если пользователь авторизован, перенаправляем на полную информацию
            return $this->sendSuccess([
                'has_access' => true,
                'redirect_to' => "/employer/worker/detail?id={$id}"
            ]);
        }

        // Проверяем, существует ли работник
        $workerExists = \app\modules\worker\models\Worker::find()
            ->where([
                'id' => $id, 
                'deleted_at' => null,
                'profile_status' => \app\modules\worker\models\Worker::PROFILE_STATUS_COMPLETE
            ])
            ->exists();

        if (!$workerExists) {
            return $this->sendNotFound($this->t('app', 'Worker not found'));
        }

        return $this->sendError(
            $this->t('app', 'Registration required to view worker details'),
            [
                'has_access' => false,
                'worker_id' => $id,
                'registration_required' => true,
                'message' => $this->t('app', 'Please register or login to view detailed worker information'),
                'actions' => [
                    'register' => '/employer/auth/send-code',
                    'login' => '/employer/auth/send-code'
                ]
            ],
            401 // Unauthorized
        );
    }

    /**
     * Разблокировать контакт работника (требует авторизации)
     *
     * GET /employer/worker/unlock-contact?id=1
     *
     * @return Response
     */
    public function actionUnlockContact()
    {
        $id = $this->getRequestData('id');

        if (empty($id)) {
            return $this->sendValidationError([
                'id' => [$this->t('app', 'Worker ID is required')]
            ]);
        }

        $employer = $this->getCurrentEmployer();
        if (!$employer) {
            return $this->sendUnauthorized();
        }

        try {
            // Проверяем доступ
            if (!$this->accessService->hasAccessToWorker($employer->id, $id)) {
                return $this->sendError(
                    $this->t('app', 'Access denied. Please purchase access to this worker first.'),
                    [
                        'worker_id' => $id,
                        'access_required' => true,
                        'purchase_url' => '/employer/access/filter-page'
                    ],
                    403
                );
            }

            // Отмечаем доступ как использованный
            $marked = $this->accessService->markWorkerAccessed($employer->id, $id);

            if (!$marked) {
                return $this->sendError($this->t('app', 'Failed to unlock contact'), null, 500);
            }

            // Получаем информацию о работнике
            $worker = \app\modules\worker\models\Worker::findOne($id);
            if (!$worker) {
                return $this->sendNotFound($this->t('app', 'Worker not found'));
            }

            // Логируем разблокировку контакта
            $this->loggingService->logWorkerView($employer, $worker->id);

            return $this->sendSuccess([
                'worker_id' => $id,
                'phone' => $worker->phone,
                'unlocked_at' => date('Y-m-d H:i:s'),
            ], $this->t('app', 'Contact unlocked successfully'));

        } catch (\Exception $e) {
            \Yii::error('Error unlocking contact: ' . $e->getMessage(), __METHOD__);
            return $this->sendError(
                $this->t('app', 'Failed to unlock contact'),
                null,
                500
            );
        }
    }
}
