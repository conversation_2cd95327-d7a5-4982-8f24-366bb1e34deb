<?php

namespace app\modules\employer\services;

/**
 * Интерфейс для платежных сервисов
 */
interface PaymentServiceInterface
{
    /**
     * Создать платеж
     * 
     * @param float $amount Сумма платежа
     * @param string $orderId Уникальный ID заказа
     * @param string $returnUrl URL для возврата после оплаты
     * @param array $additionalData Дополнительные данные
     * @return array ['payment_url' => string, 'payment_id' => string, 'status' => string]
     */
    public function createPayment($amount, $orderId, $returnUrl, $additionalData = []);

    /**
     * Обработать callback от платежной системы
     * 
     * @param array $data Данные от платежной системы
     * @return array ['status' => string, 'payment_id' => string, 'external_id' => string, 'message' => string]
     */
    public function processCallback($data);

    /**
     * Проверить статус платежа
     * 
     * @param string $paymentId ID платежа в нашей системе
     * @param string $externalId ID платежа во внешней системе
     * @return array ['status' => string, 'amount' => float, 'message' => string]
     */
    public function checkPaymentStatus($paymentId, $externalId = null);

    /**
     * Отменить платеж
     * 
     * @param string $paymentId ID платежа
     * @param string $reason Причина отмены
     * @return array ['success' => bool, 'message' => string]
     */
    public function cancelPayment($paymentId, $reason = '');

    /**
     * Получить поддерживаемые валюты
     * 
     * @return array
     */
    public function getSupportedCurrencies();

    /**
     * Получить минимальную и максимальную сумму платежа
     * 
     * @return array ['min_amount' => float, 'max_amount' => float]
     */
    public function getPaymentLimits();

    /**
     * Валидировать данные для создания платежа
     * 
     * @param float $amount
     * @param string $orderId
     * @param array $additionalData
     * @return array ['valid' => bool, 'errors' => array]
     */
    public function validatePaymentData($amount, $orderId, $additionalData = []);
}
