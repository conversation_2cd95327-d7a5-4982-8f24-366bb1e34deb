<?php

namespace app\modules\employer\services;

use app\modules\employer\models\Payment;
use app\modules\employer\models\WorkerAccessPurchase;

/**
 * Основной сервис для работы с платежами (фабрика)
 */
class PaymentService
{
    // Константы методов оплаты
    const METHOD_CLICK = 'click';
    const METHOD_PAYME = 'payme';
    const METHOD_UZCARD = 'uzcard';

    /**
     * Получить сервис для конкретного метода оплаты
     * 
     * @param string $method
     * @return PaymentServiceInterface
     * @throws \Exception
     */
    public function getPaymentService($method)
    {
        switch ($method) {
            case self::METHOD_CLICK:
                return new ClickPaymentService();
            case self::METHOD_PAYME:
                return new PaymePaymentService();
            case self::METHOD_UZCARD:
                return new UzcardPaymentService();
            default:
                throw new \Exception('Unsupported payment method: ' . $method);
        }
    }

    /**
     * Создать платеж в нашей системе
     * 
     * @param int $employerId
     * @param float $amount
     * @param string $method
     * @param int|null $purchaseId
     * @param array $additionalData
     * @return Payment|null
     */
    public function createPaymentRecord($employerId, $amount, $method, $purchaseId = null, $additionalData = [])
    {
        $payment = Payment::createPayment($employerId, $amount, $method, $purchaseId);
        
        if ($payment && !empty($additionalData)) {
            $payment->setPaymentDataArray($additionalData);
            $payment->save();
        }
        
        return $payment;
    }

    /**
     * Инициировать платеж
     * 
     * @param int $employerId
     * @param float $amount
     * @param string $method
     * @param string $returnUrl
     * @param int|null $purchaseId
     * @param array $additionalData
     * @return array
     */
    public function initiatePayment($employerId, $amount, $method, $returnUrl, $purchaseId = null, $additionalData = [])
    {
        try {
            // Создаем запись платежа в нашей системе
            $payment = $this->createPaymentRecord($employerId, $amount, $method, $purchaseId, $additionalData);
            
            if (!$payment) {
                throw new \Exception('Failed to create payment record');
            }

            // Получаем сервис для конкретного метода оплаты
            $paymentService = $this->getPaymentService($method);
            
            // Валидируем данные
            $validation = $paymentService->validatePaymentData($amount, $payment->id, $additionalData);
            if (!$validation['valid']) {
                throw new \Exception('Payment validation failed: ' . implode(', ', $validation['errors']));
            }

            // Создаем платеж во внешней системе
            $externalPayment = $paymentService->createPayment($amount, $payment->id, $returnUrl, $additionalData);
            
            // Обновляем запись платежа с данными от внешней системы
            $paymentData = $payment->getPaymentDataArray();
            $paymentData['external_payment_data'] = $externalPayment;
            $payment->setPaymentDataArray($paymentData);
            
            if (!empty($externalPayment['payment_id'])) {
                $payment->external_payment_id = $externalPayment['payment_id'];
            }
            
            $payment->save();

            return [
                'success' => true,
                'payment_id' => $payment->id,
                'payment_url' => $externalPayment['payment_url'] ?? null,
                'external_payment_id' => $externalPayment['payment_id'] ?? null,
                'message' => 'Payment initiated successfully'
            ];

        } catch (\Exception $e) {
            \Yii::error('Error initiating payment: ' . $e->getMessage(), __METHOD__);
            
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'payment_id' => isset($payment) ? $payment->id : null
            ];
        }
    }

    /**
     * Обработать callback от платежной системы
     * 
     * @param string $method
     * @param array $callbackData
     * @return array
     */
    public function processPaymentCallback($method, $callbackData)
    {
        try {
            $paymentService = $this->getPaymentService($method);
            $result = $paymentService->processCallback($callbackData);
            
            if ($result['status'] === 'completed') {
                $this->handleSuccessfulPayment($result['payment_id'], $result['external_id'] ?? null);
            } elseif ($result['status'] === 'failed') {
                $this->handleFailedPayment($result['payment_id'], $result['message'] ?? 'Payment failed');
            }
            
            return $result;
            
        } catch (\Exception $e) {
            \Yii::error('Error processing payment callback: ' . $e->getMessage(), __METHOD__);
            
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Обработать успешный платеж
     * 
     * @param string $paymentId
     * @param string|null $externalId
     * @return bool
     */
    private function handleSuccessfulPayment($paymentId, $externalId = null)
    {
        $payment = Payment::findOne($paymentId);
        if (!$payment) {
            \Yii::error('Payment not found: ' . $paymentId, __METHOD__);
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();
        
        try {
            // Отмечаем платеж как завершенный
            $payment->markAsCompleted($externalId);
            
            // Если есть связанная покупка, активируем ее
            if ($payment->purchase_id) {
                $purchase = WorkerAccessPurchase::findOne($payment->purchase_id);
                if ($purchase && $purchase->status !== WorkerAccessPurchase::STATUS_ACTIVE) {
                    $purchase->status = WorkerAccessPurchase::STATUS_ACTIVE;
                    $purchase->save();
                }
            }
            
            $transaction->commit();
            
            // Логируем успешный платеж
            \Yii::info('Payment completed successfully: ' . $paymentId, __METHOD__);
            
            return true;
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            \Yii::error('Error handling successful payment: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Обработать неудачный платеж
     * 
     * @param string $paymentId
     * @param string $reason
     * @return bool
     */
    private function handleFailedPayment($paymentId, $reason)
    {
        $payment = Payment::findOne($paymentId);
        if (!$payment) {
            \Yii::error('Payment not found: ' . $paymentId, __METHOD__);
            return false;
        }

        $payment->markAsFailed($reason);
        
        // Логируем неудачный платеж
        \Yii::warning('Payment failed: ' . $paymentId . ', reason: ' . $reason, __METHOD__);
        
        return true;
    }

    /**
     * Получить статус платежа
     * 
     * @param int $paymentId
     * @return array
     */
    public function getPaymentStatus($paymentId)
    {
        $payment = Payment::findOne($paymentId);
        if (!$payment) {
            return [
                'found' => false,
                'message' => 'Payment not found'
            ];
        }

        return [
            'found' => true,
            'payment' => $payment->toApiArray(),
            'status' => $payment->payment_status,
            'is_completed' => $payment->isCompleted(),
            'is_pending' => $payment->isPending(),
            'is_failed' => $payment->isFailed()
        ];
    }

    /**
     * Получить поддерживаемые методы оплаты
     * 
     * @return array
     */
    public function getSupportedMethods()
    {
        return [
            self::METHOD_CLICK => [
                'name' => 'Click',
                'description' => 'Оплата через Click',
                'icon' => 'click-icon.png'
            ],
            self::METHOD_PAYME => [
                'name' => 'Payme',
                'description' => 'Оплата через Payme',
                'icon' => 'payme-icon.png'
            ],
            self::METHOD_UZCARD => [
                'name' => 'UzCard',
                'description' => 'Оплата через UzCard',
                'icon' => 'uzcard-icon.png'
            ]
        ];
    }

    /**
     * Получить статистику платежей
     * 
     * @param array $filters
     * @return array
     */
    public function getPaymentStatistics($filters = [])
    {
        $dateFrom = $filters['date_from'] ?? date('Y-m-01'); // начало месяца
        $dateTo = $filters['date_to'] ?? date('Y-m-d'); // сегодня
        $method = $filters['method'] ?? null;

        return Payment::getPaymentStatistics($method, $dateFrom, $dateTo);
    }
}
