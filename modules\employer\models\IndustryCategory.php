<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use app\modules\worker\models\Profession;

/**
 * Модель отрасли (обертка для существующей таблицы industry_categories)
 * 
 * @property int $id
 * @property string $name
 * @property string $created_at
 * 
 * @property ProfessionIndustry[] $professionIndustries
 * @property Profession[] $professions
 */
class IndustryCategory extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%industry_categories}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['name'], 'string', 'max' => 255],
            [['created_at'], 'safe'],
            [['name'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Название отрасли',
            'created_at' => 'Дата создания',
        ];
    }

    /**
     * Получение связей с профессиями
     */
    public function getProfessionIndustries()
    {
        return $this->hasMany(ProfessionIndustry::class, ['industry_category_id' => 'id']);
    }

    /**
     * Получение профессий через связующую таблицу
     */
    public function getProfessions()
    {
        return $this->hasMany(Profession::class, ['id' => 'profession_id'])
            ->viaTable('{{%profession_industries}}', ['industry_category_id' => 'id']);
    }

    /**
     * Получить количество работников в отрасли
     * 
     * @return int
     */
    public function getWorkersCount()
    {
        return \app\modules\worker\models\Worker::find()
            ->alias('w')
            ->innerJoin('{{%worker_professions}} wp', 'wp.worker_id = w.id')
            ->innerJoin('{{%profession_industries}} pi', 'pi.profession_id = wp.profession_id')
            ->where([
                'pi.industry_category_id' => $this->id,
                'w.deleted_at' => null,
                'w.profile_status' => \app\modules\worker\models\Worker::PROFILE_STATUS_COMPLETE
            ])
            ->count();
    }

    /**
     * Получить все активные отрасли
     * 
     * @return array
     */
    public static function getActiveIndustries()
    {
        return static::find()
            ->orderBy('name ASC')
            ->all();
    }

    /**
     * Получить отрасли с количеством работников
     * 
     * @return array
     */
    public static function getIndustriesWithWorkerCount()
    {
        $industries = static::getActiveIndustries();
        $result = [];
        
        foreach ($industries as $industry) {
            $result[] = [
                'id' => $industry->id,
                'name' => $industry->name,
                'workers_count' => $industry->getWorkersCount(),
            ];
        }
        
        return $result;
    }
}
