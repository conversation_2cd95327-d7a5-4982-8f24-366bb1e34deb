<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use app\modules\worker\models\Worker;

/**
 * Модель ценообразования работников
 * 
 * @property int $id
 * @property int $worker_id
 * @property int $industry_category_id
 * @property float $base_price
 * @property float $premium_multiplier
 * @property bool $is_featured
 * @property string $created_at
 * @property string $updated_at
 * 
 * @property Worker $worker
 * @property IndustryCategory $industryCategory
 */
class WorkerPricing extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%worker_pricing}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['worker_id', 'base_price'], 'required'],
            [['worker_id', 'industry_category_id'], 'integer'],
            [['base_price', 'premium_multiplier'], 'number'],
            [['is_featured'], 'boolean'],
            [['created_at', 'updated_at'], 'safe'],
            [['base_price'], 'number', 'min' => 0],
            [['premium_multiplier'], 'number', 'min' => 1],
            [['premium_multiplier'], 'default', 'value' => 1.0],
            [['is_featured'], 'default', 'value' => false],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'worker_id' => 'Работник',
            'industry_category_id' => 'Отрасль',
            'base_price' => 'Базовая цена',
            'premium_multiplier' => 'Премиум множитель',
            'is_featured' => 'Рекомендуемый',
            'created_at' => 'Дата создания',
            'updated_at' => 'Дата обновления',
        ];
    }

    /**
     * Получение работника
     */
    public function getWorker()
    {
        return $this->hasOne(Worker::class, ['id' => 'worker_id']);
    }

    /**
     * Получение отрасли
     */
    public function getIndustryCategory()
    {
        return $this->hasOne(IndustryCategory::class, ['id' => 'industry_category_id']);
    }

    /**
     * Рассчитать итоговую цену с учетом множителей
     * 
     * @param TariffPlan|null $tariff
     * @return float
     */
    public function calculateFinalPrice($tariff = null)
    {
        $price = $this->base_price;
        
        // Применяем премиум множитель если работник рекомендуемый
        if ($this->is_featured) {
            $multiplier = $tariff ? $tariff->featured_multiplier : $this->premium_multiplier;
            $price *= $multiplier;
        }
        
        return $price;
    }

    /**
     * Получить цену работника для конкретной отрасли
     * 
     * @param int $workerId
     * @param int|null $industryId
     * @return static|null
     */
    public static function findWorkerPrice($workerId, $industryId = null)
    {
        $query = static::find()->where(['worker_id' => $workerId]);
        
        if ($industryId) {
            $query->andWhere(['industry_category_id' => $industryId]);
        } else {
            $query->andWhere(['industry_category_id' => null]);
        }
        
        return $query->one();
    }

    /**
     * Получить или создать цену для работника
     * 
     * @param int $workerId
     * @param int|null $industryId
     * @param float $defaultPrice
     * @return static
     */
    public static function getOrCreatePrice($workerId, $industryId = null, $defaultPrice = 5000)
    {
        $pricing = static::findWorkerPrice($workerId, $industryId);
        
        if (!$pricing) {
            $pricing = new static();
            $pricing->worker_id = $workerId;
            $pricing->industry_category_id = $industryId;
            $pricing->base_price = $defaultPrice;
            $pricing->save();
        }
        
        return $pricing;
    }

    /**
     * Установить работника как рекомендуемого
     * 
     * @param int $workerId
     * @param bool $featured
     * @param int|null $industryId
     * @return bool
     */
    public static function setWorkerFeatured($workerId, $featured = true, $industryId = null)
    {
        $pricing = static::getOrCreatePrice($workerId, $industryId);
        $pricing->is_featured = $featured;
        return $pricing->save();
    }

    /**
     * Получить рекомендуемых работников
     * 
     * @param int|null $industryId
     * @param int $limit
     * @return array
     */
    public static function getFeaturedWorkers($industryId = null, $limit = 10)
    {
        $query = static::find()
            ->with('worker')
            ->where(['is_featured' => true]);
            
        if ($industryId) {
            $query->andWhere(['industry_category_id' => $industryId]);
        }
        
        return $query->limit($limit)->all();
    }

    /**
     * Массовое обновление цен для отрасли
     * 
     * @param int $industryId
     * @param float $newPrice
     * @return int количество обновленных записей
     */
    public static function updateIndustryPrices($industryId, $newPrice)
    {
        return static::updateAll(
            ['base_price' => $newPrice, 'updated_at' => date('Y-m-d H:i:s')],
            ['industry_category_id' => $industryId]
        );
    }

    /**
     * Получить статистику цен по отраслям
     * 
     * @return array
     */
    public static function getPriceStatistics()
    {
        return static::find()
            ->select([
                'industry_category_id',
                'AVG(base_price) as avg_price',
                'MIN(base_price) as min_price',
                'MAX(base_price) as max_price',
                'COUNT(*) as workers_count'
            ])
            ->groupBy('industry_category_id')
            ->asArray()
            ->all();
    }

    /**
     * Форматировать для API
     * 
     * @return array
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'worker_id' => $this->worker_id,
            'industry_category_id' => $this->industry_category_id,
            'base_price' => $this->base_price,
            'premium_multiplier' => $this->premium_multiplier,
            'is_featured' => $this->is_featured,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
