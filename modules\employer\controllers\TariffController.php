<?php

namespace app\modules\employer\controllers;

use Yii;
use yii\rest\Controller;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\ContentNegotiator;
use yii\web\Response;
use app\modules\employer\services\TariffService;
use app\modules\employer\services\PricingService;
use app\modules\common\models\ApiResponse;

/**
 * Контроллер для работы с тарифными планами
 */
class TariffController extends Controller
{
    /**
     * @var TariffService
     */
    private $tariffService;

    /**
     * @var PricingService
     */
    private $pricingService;

    public function init()
    {
        parent::init();
        $this->tariffService = new TariffService();
        $this->pricingService = new PricingService();
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'authenticator' => [
                'class' => HttpBearerAuth::class,
            ],
            'contentNegotiator' => [
                'class' => ContentNegotiator::class,
                'formats' => [
                    'application/json' => Response::FORMAT_JSON,
                ],
            ],
        ];
    }

    /**
     * Получить список доступных тарифных планов
     * GET /employer/tariff/plans
     */
    public function actionPlans()
    {
        try {
            $workerCount = (int)Yii::$app->request->get('worker_count', 1);
            $showRecommendations = Yii::$app->request->get('show_recommendations', false);

            if ($showRecommendations && $workerCount > 0) {
                // Возвращаем тарифы с рекомендациями
                $tariffs = $this->tariffService->getRecommendedTariffs($workerCount);
            } else {
                // Возвращаем все активные тарифы
                $tariffs = $this->tariffService->getActiveTariffs();
            }

            return ApiResponse::success([
                'tariffs' => $tariffs,
                'worker_count' => $workerCount,
                'show_recommendations' => $showRecommendations
            ]);

        } catch (\Exception $e) {
            Yii::error('Error getting tariff plans: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get tariff plans', 500);
        }
    }

    /**
     * Получить детальную информацию о тарифе
     * GET /employer/tariff/detail
     */
    public function actionDetail()
    {
        try {
            $tariffId = Yii::$app->request->get('tariff_id');

            if (!$tariffId) {
                return ApiResponse::error('Tariff ID is required');
            }

            $tariff = $this->tariffService->getTariffById($tariffId);

            if (!$tariff) {
                return ApiResponse::error('Tariff not found');
            }

            return ApiResponse::success($tariff);

        } catch (\Exception $e) {
            Yii::error('Error getting tariff detail: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get tariff detail', 500);
        }
    }

    /**
     * Сравнить тарифы для определенного количества работников
     * GET /employer/tariff/compare
     */
    public function actionCompare()
    {
        try {
            $workerIds = Yii::$app->request->get('worker_ids', []);
            
            if (empty($workerIds)) {
                return ApiResponse::error('Worker IDs are required');
            }

            $comparison = $this->pricingService->calculatePricesForAllTariffs($workerIds);

            // Сортируем по итоговой цене
            usort($comparison, function($a, $b) {
                return $a['pricing']['final_price'] <=> $b['pricing']['final_price'];
            });

            return ApiResponse::success([
                'worker_count' => count($workerIds),
                'tariff_comparison' => $comparison,
                'best_value' => $comparison[0] ?? null
            ]);

        } catch (\Exception $e) {
            Yii::error('Error comparing tariffs: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to compare tariffs', 500);
        }
    }

    /**
     * Получить тарифы с фильтрацией
     * GET /employer/tariff/filtered
     */
    public function actionFiltered()
    {
        try {
            $filters = [
                'is_active' => Yii::$app->request->get('is_active', true),
                'min_duration' => Yii::$app->request->get('min_duration'),
                'max_duration' => Yii::$app->request->get('max_duration'),
                'min_price' => Yii::$app->request->get('min_price'),
                'max_price' => Yii::$app->request->get('max_price'),
            ];

            // Убираем пустые фильтры
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            $tariffs = $this->tariffService->getTariffsWithFilters($filters);

            return ApiResponse::success([
                'tariffs' => $tariffs,
                'filters_applied' => $filters,
                'total_count' => count($tariffs)
            ]);

        } catch (\Exception $e) {
            Yii::error('Error getting filtered tariffs: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get filtered tariffs', 500);
        }
    }

    /**
     * Получить статистику использования тарифов
     * GET /employer/tariff/statistics
     */
    public function actionStatistics()
    {
        try {
            $statistics = $this->tariffService->getTariffUsageStatistics();

            return ApiResponse::success([
                'tariff_statistics' => $statistics,
                'total_tariffs' => count($statistics),
                'generated_at' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            Yii::error('Error getting tariff statistics: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get tariff statistics', 500);
        }
    }

    /**
     * Получить рекомендуемый тариф для количества работников
     * GET /employer/tariff/recommended
     */
    public function actionRecommended()
    {
        try {
            $workerCount = (int)Yii::$app->request->get('worker_count', 1);

            if ($workerCount < 1) {
                return ApiResponse::error('Worker count must be at least 1');
            }

            $recommendations = $this->tariffService->getRecommendedTariffs($workerCount);
            $bestTariff = null;

            // Находим рекомендуемый тариф
            foreach ($recommendations as $recommendation) {
                if ($recommendation['is_recommended']) {
                    $bestTariff = $recommendation;
                    break;
                }
            }

            return ApiResponse::success([
                'worker_count' => $workerCount,
                'recommended_tariff' => $bestTariff,
                'all_recommendations' => $recommendations
            ]);

        } catch (\Exception $e) {
            Yii::error('Error getting recommended tariff: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get recommended tariff', 500);
        }
    }

    /**
     * Получить информацию о скидках для тарифа
     * GET /employer/tariff/discounts
     */
    public function actionDiscounts()
    {
        try {
            $tariffId = Yii::$app->request->get('tariff_id');
            $workerCount = (int)Yii::$app->request->get('worker_count', 1);

            if (!$tariffId) {
                return ApiResponse::error('Tariff ID is required');
            }

            $tariff = \app\modules\employer\models\TariffPlan::findOne($tariffId);
            if (!$tariff) {
                return ApiResponse::error('Tariff not found');
            }

            $volumeDiscount = $tariff->calculateVolumeDiscount($workerCount);
            $totalDiscount = max($volumeDiscount, $tariff->discount_percent);

            return ApiResponse::success([
                'tariff_id' => $tariffId,
                'worker_count' => $workerCount,
                'base_discount_percent' => $tariff->discount_percent,
                'volume_discount_percent' => $volumeDiscount,
                'total_discount_percent' => $totalDiscount,
                'volume_discounts_available' => $tariff->getVolumeDiscountsArray(),
                'savings_info' => [
                    'base_price_per_worker' => $tariff->base_price_per_worker,
                    'total_base_price' => $tariff->base_price_per_worker * $workerCount,
                    'discount_amount' => ($tariff->base_price_per_worker * $workerCount) * ($totalDiscount / 100),
                    'final_price' => ($tariff->base_price_per_worker * $workerCount) * (1 - $totalDiscount / 100)
                ]
            ]);

        } catch (\Exception $e) {
            Yii::error('Error getting tariff discounts: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get tariff discounts', 500);
        }
    }
}
