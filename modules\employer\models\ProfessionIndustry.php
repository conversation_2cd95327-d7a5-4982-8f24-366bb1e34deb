<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use app\modules\worker\models\Profession;

/**
 * Модель связи профессий с отраслями
 * 
 * @property int $id
 * @property int $profession_id
 * @property int $industry_category_id
 * @property string $created_at
 * 
 * @property Profession $profession
 * @property IndustryCategory $industryCategory
 */
class ProfessionIndustry extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%profession_industries}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['profession_id', 'industry_category_id'], 'required'],
            [['profession_id', 'industry_category_id'], 'integer'],
            [['created_at'], 'safe'],
            [['profession_id', 'industry_category_id'], 'unique', 
                'targetAttribute' => ['profession_id', 'industry_category_id'],
                'message' => 'This profession is already linked to this industry'
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'profession_id' => 'Профессия',
            'industry_category_id' => 'Отрасль',
            'created_at' => 'Дата создания',
        ];
    }

    /**
     * Получение профессии
     */
    public function getProfession()
    {
        return $this->hasOne(Profession::class, ['id' => 'profession_id']);
    }

    /**
     * Получение отрасли
     */
    public function getIndustryCategory()
    {
        return $this->hasOne(IndustryCategory::class, ['id' => 'industry_category_id']);
    }

    /**
     * Получить профессии по отрасли
     * 
     * @param int $industryId
     * @return array
     */
    public static function getProfessionsByIndustry($industryId)
    {
        return static::find()
            ->with('profession')
            ->where(['industry_category_id' => $industryId])
            ->all();
    }

    /**
     * Получить отрасли по профессии
     * 
     * @param int $professionId
     * @return array
     */
    public static function getIndustriesByProfession($professionId)
    {
        return static::find()
            ->with('industryCategory')
            ->where(['profession_id' => $professionId])
            ->all();
    }

    /**
     * Создать связь профессии с отраслью
     * 
     * @param int $professionId
     * @param int $industryId
     * @return static|null
     */
    public static function createLink($professionId, $industryId)
    {
        $link = new static();
        $link->profession_id = $professionId;
        $link->industry_category_id = $industryId;
        
        if ($link->save()) {
            return $link;
        }
        
        return null;
    }
}
