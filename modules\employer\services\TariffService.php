<?php

namespace app\modules\employer\services;

use app\modules\employer\models\TariffPlan;
use app\modules\employer\models\WorkerAccessPurchase;

/**
 * Сервис для работы с тарифными планами
 */
class TariffService
{
    /**
     * Получить все активные тарифы
     * 
     * @return array
     */
    public function getActiveTariffs()
    {
        $tariffs = TariffPlan::getActiveTariffs();
        $result = [];

        foreach ($tariffs as $tariff) {
            $result[] = $this->formatTariffForApi($tariff);
        }

        return $result;
    }

    /**
     * Получить тариф по ID
     * 
     * @param int $tariffId
     * @return array|null
     */
    public function getTariffById($tariffId)
    {
        $tariff = TariffPlan::findOne($tariffId);
        
        if (!$tariff || !$tariff->is_active) {
            return null;
        }

        return $this->formatTariffForApi($tariff);
    }

    /**
     * Получить рекомендуемые тарифы для количества работников
     * 
     * @param int $workerCount
     * @return array
     */
    public function getRecommendedTariffs($workerCount)
    {
        $tariffs = TariffPlan::getActiveTariffs();
        $recommendations = [];

        foreach ($tariffs as $tariff) {
            $volumeDiscount = $tariff->calculateVolumeDiscount($workerCount);
            $totalDiscount = max($volumeDiscount, $tariff->discount_percent);
            
            // Рассчитываем "ценность" тарифа
            $value = $this->calculateTariffValue($tariff, $workerCount);
            
            $recommendations[] = [
                'tariff' => $this->formatTariffForApi($tariff),
                'applicable_discount' => $totalDiscount,
                'volume_discount' => $volumeDiscount,
                'value_score' => $value,
                'is_recommended' => false // будет установлено ниже
            ];
        }

        // Сортируем по ценности
        usort($recommendations, function($a, $b) {
            return $b['value_score'] <=> $a['value_score'];
        });

        // Отмечаем лучший тариф как рекомендуемый
        if (!empty($recommendations)) {
            $recommendations[0]['is_recommended'] = true;
        }

        return $recommendations;
    }

    /**
     * Создать новый тариф
     * 
     * @param array $data
     * @return TariffPlan|null
     */
    public function createTariff($data)
    {
        $tariff = new TariffPlan();
        $tariff->name = $data['name'];
        $tariff->duration_days = $data['duration_days'];
        $tariff->base_price_per_worker = $data['base_price_per_worker'];
        $tariff->discount_percent = $data['discount_percent'] ?? 0;
        $tariff->featured_multiplier = $data['featured_multiplier'] ?? 1.0;
        $tariff->sort_order = $data['sort_order'] ?? 0;
        
        if (isset($data['volume_discounts'])) {
            $tariff->volume_discounts = json_encode($data['volume_discounts']);
        }
        
        if (isset($data['features'])) {
            $tariff->features = json_encode($data['features']);
        }

        if ($tariff->save()) {
            return $tariff;
        }

        return null;
    }

    /**
     * Обновить тариф
     * 
     * @param int $tariffId
     * @param array $data
     * @return bool
     */
    public function updateTariff($tariffId, $data)
    {
        $tariff = TariffPlan::findOne($tariffId);
        if (!$tariff) {
            return false;
        }

        $tariff->setAttributes($data, false);
        
        if (isset($data['volume_discounts'])) {
            $tariff->volume_discounts = json_encode($data['volume_discounts']);
        }
        
        if (isset($data['features'])) {
            $tariff->features = json_encode($data['features']);
        }

        return $tariff->save();
    }

    /**
     * Деактивировать тариф
     * 
     * @param int $tariffId
     * @return bool
     */
    public function deactivateTariff($tariffId)
    {
        $tariff = TariffPlan::findOne($tariffId);
        if (!$tariff) {
            return false;
        }

        $tariff->is_active = false;
        return $tariff->save();
    }

    /**
     * Получить статистику использования тарифов
     * 
     * @return array
     */
    public function getTariffUsageStatistics()
    {
        $tariffs = TariffPlan::find()->all();
        $statistics = [];

        foreach ($tariffs as $tariff) {
            $purchasesCount = WorkerAccessPurchase::find()
                ->where(['tariff_plan_id' => $tariff->id])
                ->count();

            $activePurchasesCount = WorkerAccessPurchase::find()
                ->where([
                    'tariff_plan_id' => $tariff->id,
                    'status' => WorkerAccessPurchase::STATUS_ACTIVE
                ])
                ->andWhere(['>', 'expires_at', date('Y-m-d H:i:s')])
                ->count();

            $totalRevenue = WorkerAccessPurchase::find()
                ->where(['tariff_plan_id' => $tariff->id])
                ->sum('final_price') ?: 0;

            $statistics[] = [
                'tariff' => $this->formatTariffForApi($tariff),
                'total_purchases' => $purchasesCount,
                'active_purchases' => $activePurchasesCount,
                'total_revenue' => $totalRevenue,
                'average_purchase_amount' => $purchasesCount > 0 ? $totalRevenue / $purchasesCount : 0
            ];
        }

        // Сортируем по популярности
        usort($statistics, function($a, $b) {
            return $b['total_purchases'] - $a['total_purchases'];
        });

        return $statistics;
    }

    /**
     * Получить тарифы с фильтрацией
     * 
     * @param array $filters
     * @return array
     */
    public function getTariffsWithFilters($filters = [])
    {
        $query = TariffPlan::find();

        if (isset($filters['is_active'])) {
            $query->andWhere(['is_active' => $filters['is_active']]);
        }

        if (isset($filters['min_duration'])) {
            $query->andWhere(['>=', 'duration_days', $filters['min_duration']]);
        }

        if (isset($filters['max_duration'])) {
            $query->andWhere(['<=', 'duration_days', $filters['max_duration']]);
        }

        if (isset($filters['min_price'])) {
            $query->andWhere(['>=', 'base_price_per_worker', $filters['min_price']]);
        }

        if (isset($filters['max_price'])) {
            $query->andWhere(['<=', 'base_price_per_worker', $filters['max_price']]);
        }

        $tariffs = $query->orderBy('sort_order ASC, name ASC')->all();
        $result = [];

        foreach ($tariffs as $tariff) {
            $result[] = $this->formatTariffForApi($tariff);
        }

        return $result;
    }

    /**
     * Рассчитать ценность тарифа для определенного количества работников
     * 
     * @param TariffPlan $tariff
     * @param int $workerCount
     * @return float
     */
    private function calculateTariffValue($tariff, $workerCount)
    {
        $volumeDiscount = $tariff->calculateVolumeDiscount($workerCount);
        $totalDiscount = max($volumeDiscount, $tariff->discount_percent);
        
        // Базовая ценность = скидка
        $value = $totalDiscount;
        
        // Бонус за длительность (больше дней = больше ценность)
        $durationBonus = min($tariff->duration_days / 30, 3); // максимум 3 балла за длительность
        $value += $durationBonus;
        
        // Бонус за дополнительные возможности
        $features = $tariff->getFeaturesArray();
        $featuresBonus = count($features) * 0.5; // 0.5 балла за каждую возможность
        $value += $featuresBonus;
        
        return $value;
    }

    /**
     * Форматировать тариф для API
     * 
     * @param TariffPlan $tariff
     * @return array
     */
    private function formatTariffForApi($tariff)
    {
        return [
            'id' => $tariff->id,
            'name' => $tariff->name,
            'duration_days' => $tariff->duration_days,
            'duration_text' => $this->formatDuration($tariff->duration_days),
            'base_price_per_worker' => $tariff->base_price_per_worker,
            'discount_percent' => $tariff->discount_percent,
            'featured_multiplier' => $tariff->featured_multiplier,
            'volume_discounts' => $tariff->getVolumeDiscountsArray(),
            'features' => $tariff->getFeaturesArray(),
            'sort_order' => $tariff->sort_order,
            'is_active' => $tariff->is_active,
            'created_at' => $tariff->created_at
        ];
    }

    /**
     * Форматировать длительность в читаемый вид
     * 
     * @param int $days
     * @return string
     */
    private function formatDuration($days)
    {
        if ($days < 7) {
            return $days . ' дн.';
        } elseif ($days < 30) {
            $weeks = floor($days / 7);
            return $weeks . ' нед.';
        } elseif ($days < 365) {
            $months = floor($days / 30);
            return $months . ' мес.';
        } else {
            $years = floor($days / 365);
            return $years . ' г.';
        }
    }
}
