<?php

use yii\db\Migration;

/**
 * Создание таблицы тарифных планов
 */
class m241223_000001_create_tariff_plans_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%tariff_plans}}', [
            'id' => $this->primaryKey(),
            'name_uz' => $this->string(100)->notNull()->comment('Название тарифа на узбекском'),
            'name_ru' => $this->string(100)->notNull()->comment('Название тарифа на русском'),
            'name_en' => $this->string(100)->notNull()->comment('Название тарифа на английском'),
            'description_uz' => $this->text()->comment('Описание тарифа на узбекском'),
            'description_ru' => $this->text()->comment('Описание тарифа на русском'),
            'description_en' => $this->text()->comment('Описание тарифа на английском'),
            'base_price_per_worker' => $this->decimal(10, 2)->notNull()->comment('Базовая цена за одного работника'),
            'duration_days' => $this->integer()->notNull()->comment('Длительность доступа в днях'),
            'discount_percent' => $this->decimal(5, 2)->defaultValue(0)->comment('Процент скидки'),
            'volume_discounts' => $this->json()->comment('Объемные скидки в формате JSON'),
            'features' => $this->json()->comment('Особенности тарифа в формате JSON'),
            'is_active' => $this->boolean()->defaultValue(true)->comment('Активен ли тариф'),
            'is_default' => $this->boolean()->defaultValue(false)->comment('Тариф по умолчанию'),
            'sort_order' => $this->integer()->defaultValue(0)->comment('Порядок сортировки'),
            'created_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP'),
            'updated_at' => $this->timestamp()->defaultExpression('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Индексы
        $this->createIndex('idx_tariff_plans_active', '{{%tariff_plans}}', 'is_active');
        $this->createIndex('idx_tariff_plans_default', '{{%tariff_plans}}', 'is_default');
        $this->createIndex('idx_tariff_plans_sort', '{{%tariff_plans}}', 'sort_order');
        $this->createIndex('idx_tariff_plans_deleted', '{{%tariff_plans}}', 'deleted_at');

        // Вставляем тестовые тарифы
        $this->insertDefaultTariffs();
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%tariff_plans}}');
    }

    /**
     * Вставка тарифов по умолчанию
     */
    private function insertDefaultTariffs()
    {
        $tariffs = [
            [
                'name_uz' => 'Boshlang\'ich',
                'name_ru' => 'Начальный',
                'name_en' => 'Basic',
                'description_uz' => 'Kichik biznes uchun ideal',
                'description_ru' => 'Идеально для малого бизнеса',
                'description_en' => 'Perfect for small business',
                'base_price_per_worker' => 5000.00,
                'duration_days' => 30,
                'discount_percent' => 0,
                'volume_discounts' => json_encode([
                    ['min_workers' => 10, 'discount_percent' => 5],
                    ['min_workers' => 25, 'discount_percent' => 10],
                    ['min_workers' => 50, 'discount_percent' => 15],
                ]),
                'features' => json_encode([
                    'uz' => ['Telefon raqamiga kirish', '30 kun davomida'],
                    'ru' => ['Доступ к телефонам', 'На 30 дней'],
                    'en' => ['Phone access', 'For 30 days']
                ]),
                'is_active' => 1,
                'is_default' => 1,
                'sort_order' => 1,
            ],
            [
                'name_uz' => 'Standart',
                'name_ru' => 'Стандартный',
                'name_en' => 'Standard',
                'description_uz' => 'O\'rta biznes uchun',
                'description_ru' => 'Для среднего бизнеса',
                'description_en' => 'For medium business',
                'base_price_per_worker' => 4500.00,
                'duration_days' => 60,
                'discount_percent' => 10,
                'volume_discounts' => json_encode([
                    ['min_workers' => 10, 'discount_percent' => 15],
                    ['min_workers' => 25, 'discount_percent' => 20],
                    ['min_workers' => 50, 'discount_percent' => 25],
                ]),
                'features' => json_encode([
                    'uz' => ['Telefon raqamiga kirish', '60 kun davomida', '10% chegirma'],
                    'ru' => ['Доступ к телефонам', 'На 60 дней', 'Скидка 10%'],
                    'en' => ['Phone access', 'For 60 days', '10% discount']
                ]),
                'is_active' => 1,
                'is_default' => 0,
                'sort_order' => 2,
            ],
            [
                'name_uz' => 'Premium',
                'name_ru' => 'Премиум',
                'name_en' => 'Premium',
                'description_uz' => 'Katta biznes uchun',
                'description_ru' => 'Для крупного бизнеса',
                'description_en' => 'For large business',
                'base_price_per_worker' => 4000.00,
                'duration_days' => 90,
                'discount_percent' => 20,
                'volume_discounts' => json_encode([
                    ['min_workers' => 10, 'discount_percent' => 25],
                    ['min_workers' => 25, 'discount_percent' => 30],
                    ['min_workers' => 50, 'discount_percent' => 35],
                ]),
                'features' => json_encode([
                    'uz' => ['Telefon raqamiga kirish', '90 kun davomida', '20% chegirma', 'Ustuvor qo\'llab-quvvatlash'],
                    'ru' => ['Доступ к телефонам', 'На 90 дней', 'Скидка 20%', 'Приоритетная поддержка'],
                    'en' => ['Phone access', 'For 90 days', '20% discount', 'Priority support']
                ]),
                'is_active' => 1,
                'is_default' => 0,
                'sort_order' => 3,
            ],
        ];

        foreach ($tariffs as $tariff) {
            $this->insert('{{%tariff_plans}}', $tariff);
        }
    }
}
