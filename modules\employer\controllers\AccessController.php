<?php

namespace app\modules\employer\controllers;

use Yii;
use yii\rest\Controller;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\ContentNegotiator;
use yii\web\Response;
use app\modules\employer\services\AccessControlService;
use app\modules\employer\services\PricingService;
use app\modules\employer\services\IndustryService;
use app\modules\employer\services\PaymentService;
use app\modules\common\models\ApiResponse;

/**
 * Контроллер для управления доступом к контактам работников
 */
class AccessController extends Controller
{
    /**
     * @var AccessControlService
     */
    private $accessService;

    /**
     * @var PricingService
     */
    private $pricingService;

    /**
     * @var IndustryService
     */
    private $industryService;

    /**
     * @var PaymentService
     */
    private $paymentService;

    public function init()
    {
        parent::init();
        $this->accessService = new AccessControlService();
        $this->pricingService = new PricingService();
        $this->industryService = new IndustryService();
        $this->paymentService = new PaymentService();
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'authenticator' => [
                'class' => HttpBearerAuth::class,
            ],
            'contentNegotiator' => [
                'class' => ContentNegotiator::class,
                'formats' => [
                    'application/json' => Response::FORMAT_JSON,
                ],
            ],
        ];
    }

    /**
     * Страница фильтрации работников для покупки доступа
     * GET /employer/access/filter-page
     */
    public function actionFilterPage()
    {
        try {
            $industryId = Yii::$app->request->get('industry_id');
            $professionIds = Yii::$app->request->get('profession_ids', []);
            $experienceFrom = Yii::$app->request->get('experience_from');
            $experienceTo = Yii::$app->request->get('experience_to');
            $ageFrom = Yii::$app->request->get('age_from');
            $ageTo = Yii::$app->request->get('age_to');
            $latitude = Yii::$app->request->get('latitude');
            $longitude = Yii::$app->request->get('longitude');
            $radius = Yii::$app->request->get('radius');
            $page = (int)Yii::$app->request->get('page', 1);
            $limit = (int)Yii::$app->request->get('limit', 20);

            $filters = array_filter([
                'profession_ids' => $professionIds,
                'experience_from' => $experienceFrom,
                'experience_to' => $experienceTo,
                'age_from' => $ageFrom,
                'age_to' => $ageTo,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'radius' => $radius,
            ]);

            if ($industryId) {
                $result = $this->industryService->getWorkersByIndustry($industryId, $page, $limit, $filters);
            } else {
                // Если отрасль не указана, показываем всех работников
                $result = [
                    'workers' => [],
                    'pagination' => ['page' => $page, 'limit' => $limit, 'total_count' => 0, 'total_pages' => 0],
                    'industry_id' => null,
                    'filters_applied' => $filters
                ];
            }

            // Добавляем информацию о ценах для каждого работника
            $defaultTariff = \app\modules\employer\models\TariffPlan::getDefaultTariff();
            if ($defaultTariff) {
                foreach ($result['workers'] as &$worker) {
                    $worker['price'] = $this->pricingService->calculateWorkerPrice($worker->id, $defaultTariff);
                }
            }

            return ApiResponse::success($result);

        } catch (\Exception $e) {
            Yii::error('Error in filter page: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to load workers', 500);
        }
    }

    /**
     * Расчет стоимости доступа к выбранным работникам
     * POST /employer/access/calculate-price
     */
    public function actionCalculatePrice()
    {
        try {
            $request = Yii::$app->request->getBodyParams();
            $workerIds = $request['worker_ids'] ?? [];
            $tariffPlanId = $request['tariff_plan_id'] ?? null;

            if (empty($workerIds)) {
                return ApiResponse::error('Worker IDs are required');
            }

            if (!$tariffPlanId) {
                return ApiResponse::error('Tariff plan ID is required');
            }

            $pricing = $this->pricingService->calculatePrice($workerIds, $tariffPlanId);

            return ApiResponse::success($pricing);

        } catch (\Exception $e) {
            Yii::error('Error calculating price: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to calculate price: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Создание покупки доступа к работникам
     * POST /employer/access/purchase
     */
    public function actionPurchase()
    {
        try {
            $request = Yii::$app->request->getBodyParams();
            $workerIds = $request['worker_ids'] ?? [];
            $tariffPlanId = $request['tariff_plan_id'] ?? null;
            $paymentMethod = $request['payment_method'] ?? 'click';
            $returnUrl = $request['return_url'] ?? '';

            if (empty($workerIds)) {
                return ApiResponse::error('Worker IDs are required');
            }

            if (!$tariffPlanId) {
                return ApiResponse::error('Tariff plan ID is required');
            }

            $employerId = Yii::$app->user->id;

            // Создаем покупку
            $purchase = $this->accessService->createAccessPurchase($employerId, $workerIds, $tariffPlanId);

            if (!$purchase) {
                return ApiResponse::error('Failed to create purchase');
            }

            // Инициируем платеж
            $paymentResult = $this->paymentService->initiatePayment(
                $employerId,
                $purchase->final_price,
                $paymentMethod,
                $returnUrl,
                $purchase->id
            );

            if (!$paymentResult['success']) {
                return ApiResponse::error('Failed to initiate payment: ' . $paymentResult['message']);
            }

            return ApiResponse::success([
                'purchase' => $purchase->toApiArray(),
                'payment' => $paymentResult
            ]);

        } catch (\Exception $e) {
            Yii::error('Error creating purchase: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to create purchase: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Получить мои покупки доступа
     * GET /employer/access/my-purchases
     */
    public function actionMyPurchases()
    {
        try {
            $employerId = Yii::$app->user->id;
            $purchases = $this->accessService->getEmployerActivePurchases($employerId);

            return ApiResponse::success([
                'purchases' => $purchases,
                'statistics' => $this->accessService->getEmployerAccessStatistics($employerId)
            ]);

        } catch (\Exception $e) {
            Yii::error('Error getting purchases: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get purchases', 500);
        }
    }

    /**
     * Получить доступных работников
     * GET /employer/access/available
     */
    public function actionAvailable()
    {
        try {
            $employerId = Yii::$app->user->id;
            $page = (int)Yii::$app->request->get('page', 1);
            $limit = (int)Yii::$app->request->get('limit', 20);

            $result = $this->accessService->getAccessibleWorkers($employerId, $page, $limit);

            return ApiResponse::success($result);

        } catch (\Exception $e) {
            Yii::error('Error getting available workers: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get available workers', 500);
        }
    }

    /**
     * Разблокировать контакт работника
     * GET /employer/access/unlock-contact
     */
    public function actionUnlockContact()
    {
        try {
            $workerId = Yii::$app->request->get('worker_id');
            $employerId = Yii::$app->user->id;

            if (!$workerId) {
                return ApiResponse::error('Worker ID is required');
            }

            // Проверяем доступ
            if (!$this->accessService->hasAccessToWorker($employerId, $workerId)) {
                return ApiResponse::error('Access denied. Please purchase access to this worker first.');
            }

            // Отмечаем доступ как использованный
            $marked = $this->accessService->markWorkerAccessed($employerId, $workerId);

            if (!$marked) {
                return ApiResponse::error('Failed to unlock contact');
            }

            // Получаем информацию о работнике
            $worker = \app\modules\worker\models\Worker::findOne($workerId);
            if (!$worker) {
                return ApiResponse::error('Worker not found');
            }

            return ApiResponse::success([
                'worker_id' => $workerId,
                'phone' => $worker->phone,
                'unlocked_at' => date('Y-m-d H:i:s'),
                'message' => 'Contact unlocked successfully'
            ]);

        } catch (\Exception $e) {
            Yii::error('Error unlocking contact: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to unlock contact: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Проверить статус доступа к работнику
     * GET /employer/access/contact-status
     */
    public function actionContactStatus()
    {
        try {
            $workerId = Yii::$app->request->get('worker_id');
            $employerId = Yii::$app->user->id;

            if (!$workerId) {
                return ApiResponse::error('Worker ID is required');
            }

            $accessInfo = $this->accessService->getWorkerAccessInfo($employerId, $workerId);

            if (!$accessInfo) {
                return ApiResponse::success([
                    'has_access' => false,
                    'message' => 'No access to this worker'
                ]);
            }

            return ApiResponse::success($accessInfo);

        } catch (\Exception $e) {
            Yii::error('Error checking contact status: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to check contact status', 500);
        }
    }

    /**
     * Получить статистику доступа
     * GET /employer/access/statistics
     */
    public function actionStatistics()
    {
        try {
            $employerId = Yii::$app->user->id;
            $statistics = $this->accessService->getEmployerAccessStatistics($employerId);

            return ApiResponse::success($statistics);

        } catch (\Exception $e) {
            Yii::error('Error getting statistics: ' . $e->getMessage(), __METHOD__);
            return ApiResponse::error('Failed to get statistics', 500);
        }
    }
}
