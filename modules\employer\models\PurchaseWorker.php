<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use app\modules\worker\models\Worker;

/**
 * Модель работника в покупке
 * 
 * @property int $id
 * @property int $purchase_id
 * @property int $worker_id
 * @property float $individual_price
 * @property string $accessed_at
 * @property string $created_at
 * 
 * @property WorkerAccessPurchase $purchase
 * @property Worker $worker
 */
class PurchaseWorker extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%purchase_workers}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['purchase_id', 'worker_id', 'individual_price'], 'required'],
            [['purchase_id', 'worker_id'], 'integer'],
            [['individual_price'], 'number'],
            [['accessed_at', 'created_at'], 'safe'],
            [['purchase_id', 'worker_id'], 'unique', 
                'targetAttribute' => ['purchase_id', 'worker_id'],
                'message' => 'This worker is already in this purchase'
            ],
            [['individual_price'], 'number', 'min' => 0],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'purchase_id' => 'Покупка',
            'worker_id' => 'Работник',
            'individual_price' => 'Индивидуальная цена',
            'accessed_at' => 'Дата доступа',
            'created_at' => 'Дата создания',
        ];
    }

    /**
     * Получение покупки
     */
    public function getPurchase()
    {
        return $this->hasOne(WorkerAccessPurchase::class, ['id' => 'purchase_id']);
    }

    /**
     * Получение работника
     */
    public function getWorker()
    {
        return $this->hasOne(Worker::class, ['id' => 'worker_id']);
    }

    /**
     * Проверить, был ли доступ использован
     * 
     * @return bool
     */
    public function isAccessed()
    {
        return !empty($this->accessed_at);
    }

    /**
     * Отметить доступ как использованный
     * 
     * @return bool
     */
    public function markAsAccessed()
    {
        if ($this->isAccessed()) {
            return true; // Уже отмечен
        }

        $this->accessed_at = date('Y-m-d H:i:s');
        return $this->save();
    }

    /**
     * Проверить доступ работодателя к работнику
     * 
     * @param int $employerId
     * @param int $workerId
     * @return static|null
     */
    public static function findEmployerAccess($employerId, $workerId)
    {
        return static::find()
            ->alias('pw')
            ->innerJoin('{{%worker_access_purchases}} wap', 'wap.id = pw.purchase_id')
            ->where([
                'pw.worker_id' => $workerId,
                'wap.employer_id' => $employerId,
                'wap.status' => WorkerAccessPurchase::STATUS_ACTIVE
            ])
            ->andWhere(['>', 'wap.expires_at', date('Y-m-d H:i:s')])
            ->one();
    }

    /**
     * Проверить, есть ли у работодателя доступ к работнику
     * 
     * @param int $employerId
     * @param int $workerId
     * @return bool
     */
    public static function hasEmployerAccess($employerId, $workerId)
    {
        return static::findEmployerAccess($employerId, $workerId) !== null;
    }

    /**
     * Получить всех работников с доступом для работодателя
     * 
     * @param int $employerId
     * @return array
     */
    public static function getAccessibleWorkers($employerId)
    {
        return static::find()
            ->alias('pw')
            ->with('worker')
            ->innerJoin('{{%worker_access_purchases}} wap', 'wap.id = pw.purchase_id')
            ->where([
                'wap.employer_id' => $employerId,
                'wap.status' => WorkerAccessPurchase::STATUS_ACTIVE
            ])
            ->andWhere(['>', 'wap.expires_at', date('Y-m-d H:i:s')])
            ->orderBy('pw.created_at DESC')
            ->all();
    }

    /**
     * Создать записи работников для покупки
     * 
     * @param int $purchaseId
     * @param array $workerData [['worker_id' => 1, 'price' => 5000], ...]
     * @return bool
     */
    public static function createForPurchase($purchaseId, $workerData)
    {
        $transaction = \Yii::$app->db->beginTransaction();
        
        try {
            foreach ($workerData as $data) {
                $purchaseWorker = new static();
                $purchaseWorker->purchase_id = $purchaseId;
                $purchaseWorker->worker_id = $data['worker_id'];
                $purchaseWorker->individual_price = $data['price'];
                
                if (!$purchaseWorker->save()) {
                    throw new \Exception('Failed to save purchase worker: ' . json_encode($purchaseWorker->errors));
                }
            }
            
            $transaction->commit();
            return true;
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            \Yii::error('Error creating purchase workers: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Форматировать для API
     * 
     * @return array
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'worker_id' => $this->worker_id,
            'individual_price' => $this->individual_price,
            'is_accessed' => $this->isAccessed(),
            'accessed_at' => $this->accessed_at,
            'created_at' => $this->created_at,
        ];
    }
}
