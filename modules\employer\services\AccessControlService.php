<?php

namespace app\modules\employer\services;

use app\modules\employer\models\WorkerAccessPurchase;
use app\modules\employer\models\PurchaseWorker;
use app\modules\employer\models\TariffPlan;
use app\modules\employer\models\Payment;
use app\modules\worker\models\Worker;

/**
 * Сервис для контроля доступа к работникам
 */
class AccessControlService
{
    /**
     * @var PricingService
     */
    private $pricingService;

    public function __construct()
    {
        $this->pricingService = new PricingService();
    }

    /**
     * Проверить доступ работодателя к работнику
     * 
     * @param int $employerId
     * @param int $workerId
     * @return bool
     */
    public function hasAccessToWorker($employerId, $workerId)
    {
        return PurchaseWorker::hasEmployerAccess($employerId, $workerId);
    }

    /**
     * Получить информацию о доступе к работнику
     * 
     * @param int $employerId
     * @param int $workerId
     * @return array|null
     */
    public function getWorkerAccessInfo($employerId, $workerId)
    {
        $purchaseWorker = PurchaseWorker::findEmployerAccess($employerId, $workerId);
        
        if (!$purchaseWorker) {
            return null;
        }

        $purchase = $purchaseWorker->purchase;
        
        return [
            'has_access' => true,
            'is_accessed' => $purchaseWorker->isAccessed(),
            'accessed_at' => $purchaseWorker->accessed_at,
            'expires_at' => $purchase->expires_at,
            'is_active' => $purchase->isActive(),
            'purchase_id' => $purchase->id,
            'tariff_name' => $purchase->tariffPlan->name ?? null,
        ];
    }

    /**
     * Создать покупку доступа к работникам
     * 
     * @param int $employerId
     * @param array $workerIds
     * @param int $tariffPlanId
     * @return WorkerAccessPurchase|null
     */
    public function createAccessPurchase($employerId, $workerIds, $tariffPlanId)
    {
        $tariff = TariffPlan::findOne($tariffPlanId);
        if (!$tariff || !$tariff->is_active) {
            throw new \Exception('Invalid or inactive tariff plan');
        }

        // Проверяем, что все работники существуют и активны
        $workers = Worker::find()
            ->where(['id' => $workerIds])
            ->andWhere(['deleted_at' => null])
            ->andWhere(['profile_status' => Worker::PROFILE_STATUS_COMPLETE])
            ->all();

        if (count($workers) !== count($workerIds)) {
            throw new \Exception('Some workers not found or inactive');
        }

        // Рассчитываем стоимость
        $pricing = $this->pricingService->calculatePrice($workerIds, $tariffPlanId);

        $transaction = \Yii::$app->db->beginTransaction();
        
        try {
            // Создаем покупку
            $purchase = new WorkerAccessPurchase();
            $purchase->employer_id = $employerId;
            $purchase->tariff_plan_id = $tariffPlanId;
            $purchase->total_workers_count = count($workerIds);
            $purchase->base_price = $pricing['base_price'];
            $purchase->discount_amount = $pricing['discount_amount'];
            $purchase->final_price = $pricing['final_price'];
            $purchase->starts_at = date('Y-m-d H:i:s');
            $purchase->expires_at = date('Y-m-d H:i:s', strtotime('+' . $tariff->duration_days . ' days'));
            
            if (!$purchase->save()) {
                throw new \Exception('Failed to create purchase: ' . json_encode($purchase->errors));
            }

            // Создаем записи работников в покупке
            $workerData = [];
            foreach ($workerIds as $workerId) {
                $workerData[] = [
                    'worker_id' => $workerId,
                    'price' => $pricing['worker_prices'][$workerId]
                ];
            }

            if (!PurchaseWorker::createForPurchase($purchase->id, $workerData)) {
                throw new \Exception('Failed to create purchase workers');
            }

            $transaction->commit();
            return $purchase;
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            \Yii::error('Error creating access purchase: ' . $e->getMessage(), __METHOD__);
            throw $e;
        }
    }

    /**
     * Отметить доступ к работнику как использованный
     * 
     * @param int $employerId
     * @param int $workerId
     * @return bool
     */
    public function markWorkerAccessed($employerId, $workerId)
    {
        $purchaseWorker = PurchaseWorker::findEmployerAccess($employerId, $workerId);
        
        if (!$purchaseWorker) {
            return false;
        }

        return $purchaseWorker->markAsAccessed();
    }

    /**
     * Получить активные покупки работодателя
     * 
     * @param int $employerId
     * @return array
     */
    public function getEmployerActivePurchases($employerId)
    {
        $purchases = WorkerAccessPurchase::getActiveByEmployer($employerId);
        $result = [];

        foreach ($purchases as $purchase) {
            $result[] = $purchase->toApiArray();
        }

        return $result;
    }

    /**
     * Получить доступных работников для работодателя
     * 
     * @param int $employerId
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getAccessibleWorkers($employerId, $page = 1, $limit = 20)
    {
        $purchaseWorkers = PurchaseWorker::getAccessibleWorkers($employerId);
        
        // Пагинация
        $totalCount = count($purchaseWorkers);
        $offset = ($page - 1) * $limit;
        $paginatedWorkers = array_slice($purchaseWorkers, $offset, $limit);

        $workers = [];
        foreach ($paginatedWorkers as $pw) {
            if ($pw->worker) {
                $workerData = [
                    'id' => $pw->worker->id,
                    'name' => $pw->worker->name,
                    'phone' => $pw->worker->phone,
                    'age' => $pw->worker->age,
                    'experience_years' => $pw->worker->experience_years,
                    'about' => $pw->worker->about,
                    'audio_file_url' => $pw->worker->audio_file_url,
                    'location' => [
                        'lat' => $pw->worker->lat,
                        'lng' => $pw->worker->long,
                    ],
                    'access_info' => [
                        'individual_price' => $pw->individual_price,
                        'is_accessed' => $pw->isAccessed(),
                        'accessed_at' => $pw->accessed_at,
                        'purchase_id' => $pw->purchase_id,
                    ]
                ];
                
                // Добавляем профессии
                $workerData['professions'] = [];
                foreach ($pw->worker->professions as $profession) {
                    $workerData['professions'][] = [
                        'id' => $profession->id,
                        'name_uz' => $profession->name_uz,
                        'name_ru' => $profession->name_ru,
                        'name_en' => $profession->name_en,
                    ];
                }
                
                $workers[] = $workerData;
            }
        }

        return [
            'workers' => $workers,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total_count' => $totalCount,
                'total_pages' => ceil($totalCount / $limit),
            ]
        ];
    }

    /**
     * Проверить, может ли работодатель добавить работника в избранное
     * 
     * @param int $employerId
     * @param int $workerId
     * @return bool
     */
    public function canAddToFavorites($employerId, $workerId)
    {
        return $this->hasAccessToWorker($employerId, $workerId);
    }

    /**
     * Получить статистику доступа работодателя
     * 
     * @param int $employerId
     * @return array
     */
    public function getEmployerAccessStatistics($employerId)
    {
        $activePurchases = WorkerAccessPurchase::getActiveByEmployer($employerId);
        
        $totalWorkers = 0;
        $accessedWorkers = 0;
        $totalSpent = 0;
        $activeUntil = null;

        foreach ($activePurchases as $purchase) {
            $totalWorkers += $purchase->total_workers_count;
            $accessedWorkers += $purchase->getAccessedWorkersCount();
            $totalSpent += $purchase->final_price;
            
            if (!$activeUntil || strtotime($purchase->expires_at) > strtotime($activeUntil)) {
                $activeUntil = $purchase->expires_at;
            }
        }

        return [
            'active_purchases_count' => count($activePurchases),
            'total_workers_available' => $totalWorkers,
            'accessed_workers_count' => $accessedWorkers,
            'remaining_workers_count' => $totalWorkers - $accessedWorkers,
            'total_spent' => $totalSpent,
            'active_until' => $activeUntil,
            'usage_percentage' => $totalWorkers > 0 ? round(($accessedWorkers / $totalWorkers) * 100, 2) : 0
        ];
    }

    /**
     * Проверить истекающие покупки
     * 
     * @param int $daysBeforeExpiry
     * @return array
     */
    public function getExpiringPurchases($daysBeforeExpiry = 7)
    {
        $expiryDate = date('Y-m-d H:i:s', strtotime('+' . $daysBeforeExpiry . ' days'));
        
        return WorkerAccessPurchase::find()
            ->with(['employer', 'tariffPlan'])
            ->where(['status' => WorkerAccessPurchase::STATUS_ACTIVE])
            ->andWhere(['<=', 'expires_at', $expiryDate])
            ->andWhere(['>', 'expires_at', date('Y-m-d H:i:s')])
            ->all();
    }

    /**
     * Продлить доступ к покупке
     * 
     * @param int $purchaseId
     * @param int $additionalDays
     * @return bool
     */
    public function extendPurchaseAccess($purchaseId, $additionalDays)
    {
        $purchase = WorkerAccessPurchase::findOne($purchaseId);
        
        if (!$purchase || !$purchase->isActive()) {
            return false;
        }

        $newExpiryDate = date('Y-m-d H:i:s', strtotime($purchase->expires_at . ' +' . $additionalDays . ' days'));
        $purchase->expires_at = $newExpiryDate;
        
        return $purchase->save();
    }
}
