<?php

use yii\db\Migration;

/**
 * Миграция для создания системы доступа к контактам работников
 */
class m250723_120000_create_worker_access_system extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Связь профессий с отраслями (many-to-many)
        $this->createTable('{{%profession_industries}}', [
            'id' => $this->primaryKey(),
            'profession_id' => $this->integer()->notNull(),
            'industry_category_id' => $this->integer()->notNull(),
            'created_at' => $this->timestamp()->defaultExpression('NOW()'),
        ]);

        // Добавляем внешние ключи для profession_industries
        $this->addForeignKey(
            'fk-profession_industries-profession_id',
            '{{%profession_industries}}',
            'profession_id',
            '{{%professions}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-profession_industries-industry_category_id',
            '{{%profession_industries}}',
            'industry_category_id',
            '{{%industry_categories}}',
            'id',
            'CASCADE'
        );

        // Уникальный индекс для предотвращения дублирования связей
        $this->createIndex(
            'idx-profession_industries-unique',
            '{{%profession_industries}}',
            ['profession_id', 'industry_category_id'],
            true
        );

        // Тарифные планы с гибкими скидками
        $this->createTable('{{%tariff_plans}}', [
            'id' => $this->primaryKey(),
            'name' => $this->string(100)->notNull(),
            'duration_days' => $this->integer()->notNull(),
            'discount_percent' => $this->decimal(5, 2)->defaultValue(0),
            'base_price_per_worker' => $this->decimal(10, 2)->notNull(),
            'featured_multiplier' => $this->decimal(3, 2)->defaultValue(1.0),
            'volume_discounts' => $this->json(),
            'features' => $this->json(),
            'is_active' => $this->boolean()->defaultValue(true),
            'sort_order' => $this->integer()->defaultValue(0),
            'created_at' => $this->timestamp()->defaultExpression('NOW()'),
        ]);

        // Покупки доступа к работникам
        $this->createTable('{{%worker_access_purchases}}', [
            'id' => $this->primaryKey(),
            'employer_id' => $this->integer()->notNull(),
            'tariff_plan_id' => $this->integer()->notNull(),
            'total_workers_count' => $this->integer()->notNull(),
            'base_price' => $this->decimal(12, 2)->notNull(),
            'discount_amount' => $this->decimal(12, 2)->defaultValue(0),
            'final_price' => $this->decimal(12, 2)->notNull(),
            'starts_at' => $this->timestamp()->notNull(),
            'expires_at' => $this->timestamp()->notNull(),
            'status' => $this->string(20)->defaultValue('active'),
            'created_at' => $this->timestamp()->defaultExpression('NOW()'),
        ]);

        // Добавляем внешние ключи для worker_access_purchases
        $this->addForeignKey(
            'fk-worker_access_purchases-employer_id',
            '{{%worker_access_purchases}}',
            'employer_id',
            '{{%employers}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-worker_access_purchases-tariff_plan_id',
            '{{%worker_access_purchases}}',
            'tariff_plan_id',
            '{{%tariff_plans}}',
            'id',
            'CASCADE'
        );

        // Работники в покупке
        $this->createTable('{{%purchase_workers}}', [
            'id' => $this->primaryKey(),
            'purchase_id' => $this->integer()->notNull(),
            'worker_id' => $this->integer()->notNull(),
            'individual_price' => $this->decimal(10, 2)->notNull(),
            'accessed_at' => $this->timestamp()->null(),
            'created_at' => $this->timestamp()->defaultExpression('NOW()'),
        ]);

        // Добавляем внешние ключи для purchase_workers
        $this->addForeignKey(
            'fk-purchase_workers-purchase_id',
            '{{%purchase_workers}}',
            'purchase_id',
            '{{%worker_access_purchases}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-purchase_workers-worker_id',
            '{{%purchase_workers}}',
            'worker_id',
            '{{%workers}}',
            'id',
            'CASCADE'
        );

        // Уникальный индекс для предотвращения дублирования работников в покупке
        $this->createIndex(
            'idx-purchase_workers-unique',
            '{{%purchase_workers}}',
            ['purchase_id', 'worker_id'],
            true
        );

        // Ценообразование работников (опционально)
        $this->createTable('{{%worker_pricing}}', [
            'id' => $this->primaryKey(),
            'worker_id' => $this->integer()->notNull(),
            'industry_category_id' => $this->integer()->null(),
            'base_price' => $this->decimal(10, 2)->notNull(),
            'premium_multiplier' => $this->decimal(3, 2)->defaultValue(1.0),
            'is_featured' => $this->boolean()->defaultValue(false),
            'created_at' => $this->timestamp()->defaultExpression('NOW()'),
            'updated_at' => $this->timestamp()->defaultExpression('NOW()'),
        ]);

        // Добавляем внешние ключи для worker_pricing
        $this->addForeignKey(
            'fk-worker_pricing-worker_id',
            '{{%worker_pricing}}',
            'worker_id',
            '{{%workers}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-worker_pricing-industry_category_id',
            '{{%worker_pricing}}',
            'industry_category_id',
            '{{%industry_categories}}',
            'id',
            'SET NULL'
        );

        // Платежи
        $this->createTable('{{%payments}}', [
            'id' => $this->primaryKey(),
            'employer_id' => $this->integer()->notNull(),
            'purchase_id' => $this->integer()->null(),
            'amount' => $this->decimal(12, 2)->notNull(),
            'payment_method' => $this->string(20)->notNull(),
            'payment_status' => $this->string(20)->defaultValue('pending'),
            'external_payment_id' => $this->string(255)->null(),
            'payment_data' => $this->json(),
            'created_at' => $this->timestamp()->defaultExpression('NOW()'),
            'updated_at' => $this->timestamp()->defaultExpression('NOW()'),
        ]);

        // Добавляем внешние ключи для payments
        $this->addForeignKey(
            'fk-payments-employer_id',
            '{{%payments}}',
            'employer_id',
            '{{%employers}}',
            'id',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk-payments-purchase_id',
            '{{%payments}}',
            'purchase_id',
            '{{%worker_access_purchases}}',
            'id',
            'SET NULL'
        );

        // Создаем индексы для производительности
        $this->createIndex(
            'idx-purchase_workers-purchase_worker',
            '{{%purchase_workers}}',
            ['purchase_id', 'worker_id']
        );

        $this->createIndex(
            'idx-worker_access_purchases-employer_status',
            '{{%worker_access_purchases}}',
            ['employer_id', 'status', 'expires_at']
        );

        $this->createIndex(
            'idx-profession_industries-profession',
            '{{%profession_industries}}',
            'profession_id'
        );

        $this->createIndex(
            'idx-profession_industries-industry',
            '{{%profession_industries}}',
            'industry_category_id'
        );

        $this->createIndex(
            'idx-tariff_plans-active',
            '{{%tariff_plans}}',
            ['is_active', 'sort_order']
        );

        $this->createIndex(
            'idx-payments-status',
            '{{%payments}}',
            ['payment_status', 'created_at']
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        // Удаляем таблицы в обратном порядке
        $this->dropTable('{{%payments}}');
        $this->dropTable('{{%worker_pricing}}');
        $this->dropTable('{{%purchase_workers}}');
        $this->dropTable('{{%worker_access_purchases}}');
        $this->dropTable('{{%tariff_plans}}');
        $this->dropTable('{{%profession_industries}}');
    }
}
