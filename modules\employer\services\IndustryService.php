<?php

namespace app\modules\employer\services;

use app\modules\employer\models\IndustryCategory;
use app\modules\employer\models\ProfessionIndustry;
use app\modules\worker\models\Worker;
use app\modules\worker\models\Profession;
use yii\db\Query;

/**
 * Сервис для работы с отраслями
 */
class IndustryService
{
    /**
     * Получить список всех отраслей
     * 
     * @return array
     */
    public function getIndustriesList()
    {
        return IndustryCategory::getIndustriesWithWorkerCount();
    }

    /**
     * Получить профессии по отрасли
     * 
     * @param int $industryId
     * @return array
     */
    public function getProfessionsByIndustry($industryId)
    {
        $professionIndustries = ProfessionIndustry::find()
            ->with('profession')
            ->where(['industry_category_id' => $industryId])
            ->all();

        $professions = [];
        foreach ($professionIndustries as $pi) {
            if ($pi->profession) {
                $professions[] = [
                    'id' => $pi->profession->id,
                    'name_uz' => $pi->profession->name_uz,
                    'name_ru' => $pi->profession->name_ru,
                    'name_en' => $pi->profession->name_en,
                ];
            }
        }

        return $professions;
    }

    /**
     * Получить количество работников в отрасли
     * 
     * @param int $industryId
     * @return int
     */
    public function getWorkersCountByIndustry($industryId)
    {
        return (new Query())
            ->from('{{%workers}} w')
            ->innerJoin('{{%worker_professions}} wp', 'wp.worker_id = w.id')
            ->innerJoin('{{%profession_industries}} pi', 'pi.profession_id = wp.profession_id')
            ->where([
                'pi.industry_category_id' => $industryId,
                'w.deleted_at' => null,
                'w.profile_status' => Worker::PROFILE_STATUS_COMPLETE
            ])
            ->count();
    }

    /**
     * Получить работников отрасли с пагинацией
     * 
     * @param int $industryId
     * @param int $page
     * @param int $limit
     * @param array $filters
     * @return array
     */
    public function getWorkersByIndustry($industryId, $page = 1, $limit = 20, $filters = [])
    {
        $query = Worker::find()
            ->alias('w')
            ->with(['professions'])
            ->innerJoin('{{%worker_professions}} wp', 'wp.worker_id = w.id')
            ->innerJoin('{{%profession_industries}} pi', 'pi.profession_id = wp.profession_id')
            ->where([
                'pi.industry_category_id' => $industryId,
                'w.deleted_at' => null,
                'w.profile_status' => Worker::PROFILE_STATUS_COMPLETE
            ]);

        // Применяем дополнительные фильтры
        if (!empty($filters['profession_ids'])) {
            $query->andWhere(['wp.profession_id' => $filters['profession_ids']]);
        }

        if (!empty($filters['experience_from'])) {
            $query->andWhere(['>=', 'w.experience_years', $filters['experience_from']]);
        }

        if (!empty($filters['experience_to'])) {
            $query->andWhere(['<=', 'w.experience_years', $filters['experience_to']]);
        }

        if (!empty($filters['age_from'])) {
            $query->andWhere(['>=', 'w.age', $filters['age_from']]);
        }

        if (!empty($filters['age_to'])) {
            $query->andWhere(['<=', 'w.age', $filters['age_to']]);
        }

        // Фильтр по локации (радиус)
        if (!empty($filters['latitude']) && !empty($filters['longitude']) && !empty($filters['radius'])) {
            $lat = (float)$filters['latitude'];
            $lng = (float)$filters['longitude'];
            $radius = (float)$filters['radius'];
            
            // Используем формулу Haversine для расчета расстояния
            $query->andWhere([
                '<=',
                new \yii\db\Expression(
                    '(6371 * acos(cos(radians(:lat)) * cos(radians(w.lat)) * cos(radians(w.long) - radians(:lng)) + sin(radians(:lat)) * sin(radians(w.lat))))',
                    [':lat' => $lat, ':lng' => $lng]
                ),
                $radius
            ]);
        }

        // Убираем дубликаты
        $query->groupBy('w.id');

        // Подсчет общего количества
        $totalCount = $query->count();

        // Пагинация
        $offset = ($page - 1) * $limit;
        $workers = $query->offset($offset)->limit($limit)->all();

        return [
            'workers' => $workers,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total_count' => $totalCount,
                'total_pages' => ceil($totalCount / $limit),
            ],
            'industry_id' => $industryId,
            'filters_applied' => $filters
        ];
    }

    /**
     * Получить статистику по отраслям
     * 
     * @return array
     */
    public function getIndustryStatistics()
    {
        $industries = IndustryCategory::getActiveIndustries();
        $statistics = [];

        foreach ($industries as $industry) {
            $workersCount = $this->getWorkersCountByIndustry($industry->id);
            $professionsCount = count($this->getProfessionsByIndustry($industry->id));

            $statistics[] = [
                'id' => $industry->id,
                'name' => $industry->name,
                'workers_count' => $workersCount,
                'professions_count' => $professionsCount,
            ];
        }

        // Сортируем по количеству работников
        usort($statistics, function($a, $b) {
            return $b['workers_count'] - $a['workers_count'];
        });

        return $statistics;
    }

    /**
     * Создать связь профессии с отраслью
     * 
     * @param int $professionId
     * @param int $industryId
     * @return bool
     */
    public function linkProfessionToIndustry($professionId, $industryId)
    {
        $link = ProfessionIndustry::createLink($professionId, $industryId);
        return $link !== null;
    }

    /**
     * Удалить связь профессии с отраслью
     * 
     * @param int $professionId
     * @param int $industryId
     * @return bool
     */
    public function unlinkProfessionFromIndustry($professionId, $industryId)
    {
        $deleted = ProfessionIndustry::deleteAll([
            'profession_id' => $professionId,
            'industry_category_id' => $industryId
        ]);

        return $deleted > 0;
    }

    /**
     * Получить отрасли для профессии
     * 
     * @param int $professionId
     * @return array
     */
    public function getIndustriesForProfession($professionId)
    {
        $industryLinks = ProfessionIndustry::find()
            ->with('industryCategory')
            ->where(['profession_id' => $professionId])
            ->all();

        $industries = [];
        foreach ($industryLinks as $link) {
            if ($link->industryCategory) {
                $industries[] = [
                    'id' => $link->industryCategory->id,
                    'name' => $link->industryCategory->name,
                ];
            }
        }

        return $industries;
    }

    /**
     * Получить профессии не привязанные к отраслям
     * 
     * @return array
     */
    public function getUnlinkedProfessions()
    {
        $linkedProfessionIds = ProfessionIndustry::find()
            ->select('profession_id')
            ->column();

        return Profession::find()
            ->where(['not in', 'id', $linkedProfessionIds])
            ->andWhere(['deleted_at' => null])
            ->all();
    }

    /**
     * Массовое создание связей профессий с отраслью
     * 
     * @param array $professionIds
     * @param int $industryId
     * @return array результат операции
     */
    public function bulkLinkProfessionsToIndustry($professionIds, $industryId)
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($professionIds as $professionId) {
            if ($this->linkProfessionToIndustry($professionId, $industryId)) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = "Failed to link profession {$professionId}";
            }
        }

        return $results;
    }
}
